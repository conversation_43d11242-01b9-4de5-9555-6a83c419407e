// Results page functionality
let currentAnalysis = null;
let invitationId = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeResultsPage();
});

function initializeResultsPage() {
    // Obtener ID de la invitación de la URL
    const pathParts = window.location.pathname.split('/');
    invitationId = pathParts[pathParts.length - 1];
    
    if (!invitationId) {
        showErrorState('ID de invitación no válido');
        return;
    }
    
    loadAnalysis(invitationId);
    setupEventListeners();
}

async function loadAnalysis(invitationId) {
    try {
        // Primero intentar obtener el response ID del sessionStorage
        let responseId = sessionStorage.getItem('responseId');
        
        if (!responseId) {
            // Si no hay response ID, buscar por invitation ID
            const response = await fetch(`${ClaroVinculo.API_BASE}/questionnaire/${invitationId}`);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error('No se encontraron respuestas para esta invitación');
            }
        }
        
        // Obtener análisis usando el invitation ID (el backend manejará la búsqueda)
        const analysisResponse = await fetch(`${ClaroVinculo.API_BASE}/analysis/${invitationId}`);
        const analysisResult = await analysisResponse.json();
        
        if (analysisResult.success) {
            currentAnalysis = analysisResult;
            displayAnalysis(analysisResult);
        } else {
            throw new Error(analysisResult.error || 'No se pudo cargar el análisis');
        }
        
    } catch (error) {
        console.error('Error loading analysis:', error);
        showErrorState(error.message);
    }
}

function displayAnalysis(analysisData) {
    // Ocultar loading y mostrar contenido
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('resultsHeader').style.display = 'block';
    
    // Llenar información básica
    document.getElementById('respondentName').textContent = analysisData.respondent_name;
    document.getElementById('senderName').textContent = analysisData.invitation.sender_name;
    document.getElementById('analysisDate').textContent = formatDate(analysisData.analysis.created_at);
    
    // Mostrar secciones de análisis
    displayOverallAssessment(analysisData.analysis.analysis_data);
    displayKeyInsights(analysisData.analysis.insights);
    displayIndicators(analysisData.analysis.analysis_data);
    displayRecommendations(analysisData.analysis.recommendations);
    
    // Mostrar todas las secciones
    document.getElementById('overallAssessment').style.display = 'block';
    document.getElementById('keyInsights').style.display = 'block';
    document.getElementById('indicatorsSection').style.display = 'block';
    document.getElementById('recommendationsSection').style.display = 'block';
    document.getElementById('nextStepsSection').style.display = 'block';
}

function displayOverallAssessment(analysisData) {
    const container = document.getElementById('assessmentContent');
    
    let assessmentClass = 'positive';
    let assessmentIcon = 'fa-heart';
    let assessmentText = '';
    
    switch (analysisData.overall_assessment) {
        case 'positive':
            assessmentClass = 'positive';
            assessmentIcon = 'fa-heart';
            assessmentText = 'Conexión Prometedora';
            break;
        case 'mixed':
            assessmentClass = 'warning';
            assessmentIcon = 'fa-heart-pulse';
            assessmentText = 'Sentimientos Mixtos';
            break;
        case 'cautious':
            assessmentClass = 'warning';
            assessmentIcon = 'fa-exclamation-triangle';
            assessmentText = 'Proceder con Cautela';
            break;
        default:
            assessmentClass = 'positive';
            assessmentIcon = 'fa-heart';
            assessmentText = 'Análisis Completado';
    }
    
    container.innerHTML = `
        <div class="indicator ${assessmentClass}" style="font-size: 1.2rem; padding: 2rem; margin-bottom: 1rem;">
            <i class="fas ${assessmentIcon}" style="font-size: 2rem; margin-bottom: 1rem;"></i>
            <h4 style="margin: 0.5rem 0;">${assessmentText}</h4>
            <p style="margin: 0;">Nivel de interés/compatibilidad: ${analysisData.interest_level}/10</p>
        </div>
        
        <div style="background: rgba(102, 126, 234, 0.05); padding: 1.5rem; border-radius: 12px;">
            <h4 style="margin-bottom: 1rem;">Resumen del análisis:</h4>
            <p style="margin: 0; line-height: 1.6;">
                Basado en las respuestas proporcionadas, hemos identificado patrones específicos en la conexión 
                entre ${currentAnalysis.invitation.sender_name} y ${currentAnalysis.respondent_name}. 
                Los siguientes insights te ayudarán a entender mejor la dinámica de esta relación.
            </p>
        </div>
    `;
}

function displayKeyInsights(insights) {
    const container = document.getElementById('insightsContent');
    container.textContent = insights;
}

function displayIndicators(analysisData) {
    const container = document.getElementById('indicatorsContent');
    
    let indicatorsHTML = '';
    
    // Indicadores positivos
    if (analysisData.positive_signs && analysisData.positive_signs.length > 0) {
        analysisData.positive_signs.forEach(sign => {
            indicatorsHTML += `
                <div class="indicator positive">
                    <i class="fas fa-check-circle" style="margin-bottom: 0.5rem;"></i>
                    <div>${sign}</div>
                </div>
            `;
        });
    }
    
    // Señales de alerta
    if (analysisData.red_flags && analysisData.red_flags.length > 0) {
        analysisData.red_flags.forEach(flag => {
            indicatorsHTML += `
                <div class="indicator negative">
                    <i class="fas fa-exclamation-triangle" style="margin-bottom: 0.5rem;"></i>
                    <div>${flag}</div>
                </div>
            `;
        });
    }
    
    // Disponibilidad emocional
    if (analysisData.emotional_availability) {
        let availabilityClass = 'positive';
        let availabilityText = '';
        
        switch (analysisData.emotional_availability) {
            case 'high':
                availabilityClass = 'positive';
                availabilityText = 'Alta disponibilidad emocional';
                break;
            case 'moderate':
                availabilityClass = 'warning';
                availabilityText = 'Disponibilidad emocional moderada';
                break;
            case 'low':
                availabilityClass = 'negative';
                availabilityText = 'Baja disponibilidad emocional';
                break;
        }
        
        indicatorsHTML += `
            <div class="indicator ${availabilityClass}">
                <i class="fas fa-heart-pulse" style="margin-bottom: 0.5rem;"></i>
                <div>${availabilityText}</div>
            </div>
        `;
    }
    
    if (!indicatorsHTML) {
        indicatorsHTML = `
            <div class="indicator positive">
                <i class="fas fa-info-circle" style="margin-bottom: 0.5rem;"></i>
                <div>Análisis completado exitosamente</div>
            </div>
        `;
    }
    
    container.innerHTML = indicatorsHTML;
}

function displayRecommendations(recommendations) {
    const container = document.getElementById('recommendationsContent');
    container.textContent = recommendations;
}

function setupEventListeners() {
    // Botón compartir resultados
    document.getElementById('shareResultsBtn').addEventListener('click', handleShareResults);
    
    // Botón nueva invitación
    document.getElementById('newAnalysisBtn').addEventListener('click', () => {
        window.location.href = '/';
    });
    
    // Modal de compartir
    document.getElementById('closeShareModal').addEventListener('click', closeShareModal);
    document.getElementById('generateShareLink').addEventListener('click', generateShareLink);
    
    // Cerrar modal al hacer clic fuera
    document.getElementById('shareModal').addEventListener('click', (e) => {
        if (e.target.id === 'shareModal') {
            closeShareModal();
        }
    });
}

function handleShareResults() {
    document.getElementById('shareModal').style.display = 'flex';
}

function closeShareModal() {
    document.getElementById('shareModal').style.display = 'none';
}

async function generateShareLink() {
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Generando enlace...';
        
        // Simular generación de enlace compartible
        const shareableLink = `${window.location.origin}/shared-results/${invitationId}`;
        
        await ClaroVinculo.copyToClipboard(shareableLink);
        ClaroVinculo.showSuccess('¡Enlace copiado al portapapeles!');
        
        closeShareModal();
        
    } catch (error) {
        console.error('Error generating share link:', error);
        ClaroVinculo.showError('Error al generar el enlace');
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function showErrorState(message) {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('errorState').style.display = 'block';
    document.getElementById('errorMessage').textContent = message;
}

// Función para imprimir resultados
function printResults() {
    window.print();
}

// Función para descargar resultados como PDF (requeriría implementación adicional)
function downloadPDF() {
    ClaroVinculo.showNotification('Función de descarga en desarrollo', 'info');
}
