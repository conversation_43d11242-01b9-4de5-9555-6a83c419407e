{"name": "clarovinculo-mvp", "version": "1.0.0", "description": "MVP para aplicación de relaciones saludables ClaroVínculo", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["relaciones", "comunicación", "psicología", "salud mental"], "author": "Healthy Love Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "connect-sqlite3": "^0.9.13", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.7.0", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}