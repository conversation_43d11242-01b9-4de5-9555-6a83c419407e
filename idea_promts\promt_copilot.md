Aquí te presento un **prompt detallado y optimizado** que integra aspectos de psicología, sociología, relaciones humanas, desarrollo personal, ingeniería de software y marketing digital, para instruir a una inteligencia artificial en la creación de la aplicación web:

---

**Título del Prompt:**  
**Desarrollo de una Aplicación Web para Facilitar Relaciones Sanas y Conscientes**

**Contexto y Objetivo:**  
Eres una inteligencia artificial multidisciplinaria experta en psicología, sociología, relaciones humanas, ingeniería de software y marketing digital. Tu misión es diseñar y desarrollar una aplicación web llamada (temporariamente) **"Healthy Heart"** (o nombres alternativos como "Corazón Sano" o "Amor y Paz") que facilite el establecimiento de relaciones sanas y conscientes. La aplicación está orientada a parejas, familiares, amigos y nuevas conexiones, y tiene como objetivo evaluar, promover y mejorar el entendimiento y la calidad de las relaciones a través de interacciones medibles y personalizadas.

**Requerimientos Funcionales:**  

1. **Creación de Perfil e Invitación de Conexión:**  
   - **Registro y Perfiles:** Permitir que el usuario se registre creando un perfil completo y, opcionalmente, conectarse mediante Google u otros autenticadores para reducir fricción.  
   - **Invitación de Relación:** El usuario iniciador crea una “solicitud de conexión” (o “invitación al diálogo”) en la que define la relación a evaluar (por ejemplo, nueva amistad, pareja, vínculo familiar o una relación ya existente).  
   - **Enlace Personalizado:** Generar un enlace único que, al recibirlo, muestre a la persona invitada una pantalla con la foto e información del remitente y un mensaje claro, directo y empático: Ej. “Hola [Nombre], [Usuario Remitente] te invita a dialogar brevemente para conocerse mejor y comprender en qué medida podrían conectar de forma sana y positiva.”

2. **Encuesta/Evaluación Dinámica:**  
   - **Interacción Inicial:** Una vez aceptada la invitación, se ofrece la opción de crear/actualizar el perfil de forma rápida.  
   - **Modalidades de Entrada:** Brindar la posibilidad de usar texto, audio o incluso un breve video en el cual el remitente exprese un saludo o explicación del propósito.  
   - **Cuestionario Personalizado:** Diseñar una encuesta dinámica y adaptativa en función del tipo de relación seleccionada. Por ejemplo, en relaciones de “conocimiento” los ítems podrían incluir:
     - Nivel de atracción física y emocional (escala de 1 a 10).  
     - Disponibilidad sentimental y estado emocional actual.  
     - Preguntas directas como: “¿Te gustaría seguir conociéndolo/la?”, “¿Cómo definirías tu conexión actual con [nombre de la otra persona]?”  
     - Variables de respuesta: tiempo de respuesta, si responden o no, y el contenido/reacción en caso de negativa (por ejemplo, explicación breve sobre su decisión).  
   - **Lógica de Preguntas Encadenadas:** Dependiendo de la primera respuesta, el cuestionario se ramifica para profundizar en aspectos relevantes (por ejemplo, si indica baja disponibilidad emocional, la encuesta podría preguntar sobre experiencias recientes de relaciones, cierre de ciclos, etc.).

3. **Análisis y Feedback con Inteligencia Artificial:**  
   - **Procesamiento de Datos:** Una vez finalizada la encuesta, emplear un motor de IA que interprete los datos en tiempo real, generando una retroalimentación personalizada y comprensible.  
   - **Presentación del Informe:** Proveer un “análisis de relación” con frases concretas:  
     *“Según tus respuestas, hemos identificado que en estos momentos parece que no estás emocionalmente disponible para una relación seria, posiblemente porque has salido recientemente de una relación y aún estás en proceso de sanar. Esta autoconciencia te ayudará a evitar malentendidos y a proteger tanto tu bienestar como el de la otra persona.”*  
   - **Confirmación de Envío:** Antes de enviar la respuesta al usuario invitado, permitir que el remitente revise y confirme el mensaje final, ajustando el tono o el contenido si lo desea.

4. **Versatilidad para Distintas Relaciones:**  
   - Adaptar el flujo de interacción y los cuestionarios según el tipo de relación a evaluar:  
     - **Parejas consolidadas:** Evaluar la salud emocional, la comunicación, la empatía, niveles de satisfacción y áreas de mejora.  
     - **Familiares y amigos:** Medir grado de conectividad, confianza, apoyo mutuo y habilidades de comunicación.  
     - **Nuevas conexiones:** Determinar interés, disponibilidad y potencial de crecimiento conjunto.  
   - Cada módulo debe contar con un lenguaje claro, directo y sin ambigüedades para obtener datos precisos.

5. **Aspectos Técnicos y de UX/UI:**  
   - **Interfaz Amigable y Minimalista:** Diseñar una interfaz intuitiva y visualmente atractiva, donde cada paso (desde la invitación hasta el análisis final) sea claro y motivador.  
   - **Arquitectura y Seguridad:** Desarrollar una arquitectura escalable usando las mejores prácticas en ingeniería de software, garantizando la seguridad y privacidad de los datos sensibles.  
   - **Medición de Interacciones:** Incorporar métricas como el tiempo de respuesta, la tasa de aceptación y los tipos de respuestas para futuros análisis y mejoras en la experiencia de usuario.

6. **Estrategia de Marketing Digital y Posicionamiento:**  
   - **Promoción de Relaciones Sanas:** El marketing debe centrarse en la idea de crecimiento personal y bienestar emocional.  
   - **Campañas en Redes Sociales:** Generar contenidos y testimonios sobre “relaciones conscientes” y “escucha activa”.  
   - **Feedback Continuo:** Incentivar a los usuarios a compartir sus experiencias y sugerencias, lo que permitirá iterar y mejorar la plataforma.
   - **Posibles Colaboraciones:** Explorar alianzas con expertos en psicología y terapeutas para validar y enriquecer el contenido y la metodología.

**Instrucciones para la IA en Desarrollo:**  
Utiliza este prompt para estructurar un roadmap de desarrollo que contemple:

- **Fase de Diseño y Prototipado:**  
  - Definición de flujos de usuario, wireframes de cada pantalla (registro, invitación, encuesta dinámica, análisis final).  
  - Creación de prototipos de interacción y experiencia de usuario.
  
- **Fase de Desarrollo y Backend:**  
  - Diseño de la base de datos para almacenar perfiles, invitaciones y respuestas de encuestas.  
  - Implementación de autenticación social (Google, email).  
  - Programación de la lógica de encadenamiento de preguntas y respuesta adaptativa.  
  - Integración de módulos de análisis de IA para generar feedback personalizado.

- **Fase de Pruebas y Validación:**  
  - Realización de pruebas de usabilidad, verificación de métricas de respuesta y validación del algoritmo de análisis.  
  - Recabado de feedback de un grupo piloto para ajustar preguntas y el modelo de recomendaciones.

- **Lanzamiento y Estrategia de Crecimiento:**  
  - Estrategia de marketing digital, posicionamiento de la aplicación en redes sociales y colaboraciones estratégicas con profesionales de bienestar emocional y relaciones humanas.

**Tono y Lenguaje:**  
La aplicación (y, por ende, la comunicación interna de la IA) debe ser directa, empática y respetuosa. Las preguntas de la encuesta deben ir al grano sin ambigüedad, utilizando un lenguaje claro y profesional para que el usuario sienta que su bienestar emocional es la prioridad.  

---

Este prompt integral debe servir de guía para que la inteligencia artificial encargada del desarrollo vea todos los ángulos del problema: desde la correcta ingeniería de software y escalabilidad, hasta la elaboración de una experiencia que fomente relaciones saludables y conscientes, valiéndose de herramientas digitales y metodologías respaldadas por la psicología y sociología.  

Además, la estrategia de marketing y promoción orientada a un público que valora el crecimiento personal y la calidad en las relaciones refuerza el propósito de la app, posicionándola no solo como un nexo entre personas, sino como un facilitador de bienestar emocional y autoconocimiento.  

¿Te gustaría profundizar en algún apartado, como la estructura técnica de la base de datos o la formulación exacta de las preguntas de la encuesta? Estoy aquí para seguir explorando ideas relacionadas y enriquecer aún más el concepto.