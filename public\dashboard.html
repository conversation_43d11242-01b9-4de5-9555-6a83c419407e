<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - ClaroVínculo</title>
    <link rel="stylesheet" href="/styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-heart"></i>
                    <h2>ClaroVínculo</h2>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#dashboard" data-section="dashboard">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#invitations" data-section="invitations">
                            <i class="fas fa-paper-plane"></i>
                            <span>Mis Invitaciones</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#responses" data-section="responses">
                            <i class="fas fa-comments"></i>
                            <span>Respuestas</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#analytics" data-section="analytics">
                            <i class="fas fa-chart-pie"></i>
                            <span>Análisis</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#profile" data-section="profile">
                            <i class="fas fa-user"></i>
                            <span>Mi Perfil</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info" id="userInfo">
                    <!-- User info will be loaded here -->
                </div>
                <button class="btn-logout" id="logoutBtn">
                    <i class="fas fa-sign-out-alt"></i>
                    Cerrar Sesión
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="pageTitle">Dashboard</h1>
                </div>
                <div class="header-right">
                    <button class="btn-primary" id="newInvitationBtn">
                        <i class="fas fa-plus"></i>
                        Nueva Invitación
                    </button>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalInvitations">0</h3>
                            <p>Invitaciones Enviadas</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="acceptedInvitations">0</h3>
                            <p>Invitaciones Aceptadas</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingInvitations">0</h3>
                            <p>Pendientes</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalResponses">0</h3>
                            <p>Respuestas Recibidas</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3>Actividad Reciente</h3>
                        <div class="activity-list" id="recentActivity">
                            <!-- Recent activity will be loaded here -->
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>Acciones Rápidas</h3>
                        <div class="quick-actions">
                            <button class="quick-action-btn" data-action="new-invitation">
                                <i class="fas fa-plus-circle"></i>
                                <span>Nueva Invitación</span>
                            </button>
                            <button class="quick-action-btn" data-action="view-analytics">
                                <i class="fas fa-chart-bar"></i>
                                <span>Ver Análisis</span>
                            </button>
                            <button class="quick-action-btn" data-action="export-data">
                                <i class="fas fa-download"></i>
                                <span>Exportar Datos</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Invitations Section -->
            <section id="invitations-section" class="content-section">
                <div class="section-header">
                    <h2>Mis Invitaciones</h2>
                    <button class="btn-primary" id="createInvitationBtn">
                        <i class="fas fa-plus"></i>
                        Crear Invitación
                    </button>
                </div>
                
                <div class="filters">
                    <select id="statusFilter">
                        <option value="">Todos los estados</option>
                        <option value="pending">Pendientes</option>
                        <option value="accepted">Aceptadas</option>
                        <option value="declined">Rechazadas</option>
                    </select>
                    <select id="typeFilter">
                        <option value="">Todos los tipos</option>
                        <option value="nueva_conexion">Nueva Conexión</option>
                        <option value="pareja_establecida">Pareja Establecida</option>
                        <option value="amistad">Amistad</option>
                        <option value="familia">Familia</option>
                    </select>
                </div>
                
                <div class="invitations-list" id="invitationsList">
                    <!-- Invitations will be loaded here -->
                </div>
            </section>

            <!-- Responses Section -->
            <section id="responses-section" class="content-section">
                <div class="section-header">
                    <h2>Respuestas Recibidas</h2>
                </div>
                
                <div class="responses-list" id="responsesList">
                    <!-- Responses will be loaded here -->
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="content-section">
                <div class="section-header">
                    <h2>Análisis y Estadísticas</h2>
                </div>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h3>Tipos de Relación</h3>
                        <div class="chart-container" id="relationshipChart">
                            <!-- Chart will be rendered here -->
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <h3>Tasa de Respuesta</h3>
                        <div class="chart-container" id="responseChart">
                            <!-- Chart will be rendered here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile-section" class="content-section">
                <div class="section-header">
                    <h2>Mi Perfil</h2>
                </div>
                
                <div class="profile-grid">
                    <div class="profile-card">
                        <h3>Información Personal</h3>
                        <form id="profileForm">
                            <div class="form-group">
                                <label for="profileName">Nombre</label>
                                <input type="text" id="profileName" name="name">
                            </div>
                            <div class="form-group">
                                <label for="profileEmail">Email</label>
                                <input type="email" id="profileEmail" name="email">
                            </div>
                            <button type="submit" class="btn-primary">
                                Actualizar Perfil
                            </button>
                        </form>
                    </div>
                    
                    <div class="profile-card">
                        <h3>Seguridad</h3>
                        <form id="passwordForm">
                            <div class="form-group">
                                <label for="currentPassword">Contraseña Actual</label>
                                <input type="password" id="currentPassword" name="currentPassword">
                            </div>
                            <div class="form-group">
                                <label for="newPassword">Nueva Contraseña</label>
                                <input type="password" id="newPassword" name="newPassword">
                            </div>
                            <div class="form-group">
                                <label for="confirmNewPassword">Confirmar Nueva Contraseña</label>
                                <input type="password" id="confirmNewPassword" name="confirmNewPassword">
                            </div>
                            <button type="submit" class="btn-primary">
                                Cambiar Contraseña
                            </button>
                        </form>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heart fa-beat"></i>
            <p id="loadingText">Cargando...</p>
        </div>
    </div>

    <script src="/js/app.js"></script>
    <script src="/js/dashboard.js"></script>
</body>
</html>
