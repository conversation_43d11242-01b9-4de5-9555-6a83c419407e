const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const DB_PATH = process.env.DB_PATH || './database/clarovinculo.db';

// Asegurar que el directorio de la base de datos existe
const dbDir = path.dirname(DB_PATH);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const db = new sqlite3.Database(DB_PATH);

const migrateDatabase = () => {
  return new Promise((resolve, reject) => {
    console.log('🔄 Iniciando migración de base de datos...');
    
    db.serialize(() => {
      // Verificar si las nuevas columnas ya existen
      db.all("PRAGMA table_info(users)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }
        
        const columnNames = columns.map(col => col.name);
        const columnsToAdd = [];
        
        // Verificar qué columnas faltan
        if (!columnNames.includes('password')) {
          columnsToAdd.push('ALTER TABLE users ADD COLUMN password TEXT');
        }
        if (!columnNames.includes('google_id')) {
          columnsToAdd.push('ALTER TABLE users ADD COLUMN google_id TEXT');
        }
        if (!columnNames.includes('avatar_url')) {
          columnsToAdd.push('ALTER TABLE users ADD COLUMN avatar_url TEXT');
        }
        if (!columnNames.includes('email_verified')) {
          columnsToAdd.push('ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE');
        }
        if (!columnNames.includes('last_login')) {
          columnsToAdd.push('ALTER TABLE users ADD COLUMN last_login DATETIME');
        }
        if (!columnNames.includes('updated_at')) {
          columnsToAdd.push('ALTER TABLE users ADD COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP');
        }
        
        if (columnsToAdd.length === 0) {
          console.log('✅ La base de datos ya está actualizada');
          resolve();
          return;
        }
        
        console.log(`📝 Agregando ${columnsToAdd.length} columnas nuevas...`);
        
        // Ejecutar las migraciones una por una
        let completed = 0;
        let hasError = false;
        
        columnsToAdd.forEach((sql, index) => {
          db.run(sql, (err) => {
            if (err && !hasError) {
              hasError = true;
              console.error(`❌ Error en migración: ${err.message}`);
              reject(err);
              return;
            }
            
            completed++;
            console.log(`✅ Migración ${completed}/${columnsToAdd.length} completada`);
            
            if (completed === columnsToAdd.length && !hasError) {
              // Crear índices únicos para email y google_id
              db.run(`CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email)`, (err) => {
                if (err) {
                  console.warn('⚠️ No se pudo crear índice único en email:', err.message);
                }

                db.run(`CREATE UNIQUE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id)`, (err) => {
                  if (err) {
                    console.warn('⚠️ No se pudo crear índice único en google_id:', err.message);
                  }

                  console.log('🎉 Migración de base de datos completada exitosamente');
                  resolve();
                });
              });
            }
          });
        });
      });
    });
  });
};

// Ejecutar migración si este archivo se ejecuta directamente
if (require.main === module) {
  migrateDatabase()
    .then(() => {
      console.log('✅ Migración completada');
      process.exit(0);
    })
    .catch((err) => {
      console.error('❌ Error en migración:', err);
      process.exit(1);
    });
}

module.exports = migrateDatabase;
