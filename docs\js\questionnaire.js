// Questionnaire page functionality
let currentQuestionnaire = null;
let currentQuestionIndex = 0;
let responses = {};
let invitationId = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeQuestionnairePage();
});

function initializeQuestionnairePage() {
    // Obtener ID de la invitación de la URL
    const pathParts = window.location.pathname.split('/');
    invitationId = pathParts[pathParts.length - 1];
    
    if (!invitationId) {
        showErrorState('ID de invitación no válido');
        return;
    }
    
    loadQuestionnaire(invitationId);
    setupEventListeners();
}

async function loadQuestionnaire(invitationId) {
    try {
        const response = await fetch(`${ClaroVinculo.API_BASE}/questionnaire/${invitationId}`);
        const result = await response.json();
        
        if (result.success) {
            currentQuestionnaire = result.questionnaire;
            displayQuestionnaire(result.questionnaire);
        } else {
            throw new Error(result.error || 'No se pudo cargar el cuestionario');
        }
        
    } catch (error) {
        console.error('Error loading questionnaire:', error);
        showErrorState(error.message);
    }
}

function displayQuestionnaire(questionnaire) {
    // Ocultar loading y mostrar contenido
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('questionnaireHeader').style.display = 'block';
    document.getElementById('progressContainer').style.display = 'block';
    document.getElementById('questionnaireForm').style.display = 'block';
    
    // Llenar información del cuestionario
    document.getElementById('questionnaireTitle').textContent = questionnaire.title;
    document.getElementById('questionnaireDescription').textContent = questionnaire.description;
    document.getElementById('senderName').textContent = questionnaire.sender_name;
    document.getElementById('totalQuestions').textContent = questionnaire.questions.length;
    
    // Renderizar primera pregunta
    renderQuestion(0);
    updateProgress();
}

function renderQuestion(index) {
    const question = currentQuestionnaire.questions[index];
    const container = document.getElementById('questionsContainer');
    
    // Limpiar contenedor
    container.innerHTML = '';
    
    // Crear elemento de pregunta
    const questionElement = document.createElement('div');
    questionElement.className = 'question';
    questionElement.innerHTML = `
        <h3 class="question-title">${question.question}</h3>
        <div class="question-input" id="questionInput">
            ${renderQuestionInput(question)}
        </div>
    `;
    
    container.appendChild(questionElement);
    
    // Restaurar respuesta previa si existe
    if (responses[question.id]) {
        restoreResponse(question, responses[question.id]);
    }
    
    // Actualizar número de pregunta actual
    document.getElementById('currentQuestion').textContent = index + 1;
}

function renderQuestionInput(question) {
    switch (question.type) {
        case 'scale':
            return `
                <div class="scale-container">
                    <span>${question.scale.labels[question.scale.min]}</span>
                    <input type="range" 
                           class="scale-input" 
                           id="response_${question.id}"
                           min="${question.scale.min}" 
                           max="${question.scale.max}" 
                           value="${Math.floor((question.scale.min + question.scale.max) / 2)}"
                           oninput="updateScaleValue('${question.id}', this.value)">
                    <span>${question.scale.labels[question.scale.max]}</span>
                    <div class="scale-value" id="scaleValue_${question.id}">
                        ${Math.floor((question.scale.min + question.scale.max) / 2)}
                    </div>
                </div>
                <div class="scale-labels">
                    <span>${question.scale.labels[question.scale.min]}</span>
                    <span>${question.scale.labels[question.scale.max]}</span>
                </div>
            `;
            
        case 'multiple_choice':
            return `
                <div class="radio-group">
                    ${question.options.map((option, index) => `
                        <div class="radio-option">
                            <input type="radio" 
                                   id="response_${question.id}_${index}" 
                                   name="response_${question.id}" 
                                   value="${option}">
                            <label for="response_${question.id}_${index}">${option}</label>
                        </div>
                    `).join('')}
                </div>
            `;
            
        case 'text':
            return `
                <textarea id="response_${question.id}" 
                          placeholder="${question.placeholder || 'Escribe tu respuesta...'}"
                          rows="4"
                          style="width: 100%; padding: 12px; border: 2px solid #e2e8f0; border-radius: 8px; font-family: inherit; resize: vertical;"></textarea>
            `;
            
        default:
            return '<p>Tipo de pregunta no soportado</p>';
    }
}

function updateScaleValue(questionId, value) {
    document.getElementById(`scaleValue_${questionId}`).textContent = value;
    responses[questionId] = parseInt(value);
}

function restoreResponse(question, value) {
    switch (question.type) {
        case 'scale':
            const scaleInput = document.getElementById(`response_${question.id}`);
            const scaleValue = document.getElementById(`scaleValue_${question.id}`);
            scaleInput.value = value;
            scaleValue.textContent = value;
            break;
            
        case 'multiple_choice':
            const radioInput = document.querySelector(`input[name="response_${question.id}"][value="${value}"]`);
            if (radioInput) radioInput.checked = true;
            break;
            
        case 'text':
            document.getElementById(`response_${question.id}`).value = value;
            break;
    }
}

function getCurrentResponse() {
    const question = currentQuestionnaire.questions[currentQuestionIndex];
    
    switch (question.type) {
        case 'scale':
            return parseInt(document.getElementById(`response_${question.id}`).value);
            
        case 'multiple_choice':
            const checkedRadio = document.querySelector(`input[name="response_${question.id}"]:checked`);
            return checkedRadio ? checkedRadio.value : null;
            
        case 'text':
            return document.getElementById(`response_${question.id}`).value.trim();
            
        default:
            return null;
    }
}

function validateCurrentResponse() {
    const response = getCurrentResponse();
    const question = currentQuestionnaire.questions[currentQuestionIndex];
    
    if (question.type === 'text') {
        return response && response.length > 0;
    }
    
    return response !== null && response !== '';
}

function setupEventListeners() {
    // Botón siguiente
    document.getElementById('nextBtn').addEventListener('click', handleNext);
    
    // Botón anterior
    document.getElementById('prevBtn').addEventListener('click', handlePrevious);
    
    // Envío del formulario
    document.getElementById('questionnaireForm').addEventListener('submit', handleSubmit);
    
    // Botón ver resultados
    document.getElementById('viewResultsBtn').addEventListener('click', handleViewResults);
    
    // Escuchar cambios en inputs para auto-guardar
    document.addEventListener('change', function(e) {
        if (e.target.id && e.target.id.startsWith('response_')) {
            const question = currentQuestionnaire.questions[currentQuestionIndex];
            responses[question.id] = getCurrentResponse();
        }
    });
    
    document.addEventListener('input', function(e) {
        if (e.target.id && e.target.id.startsWith('response_')) {
            const question = currentQuestionnaire.questions[currentQuestionIndex];
            responses[question.id] = getCurrentResponse();
        }
    });
}

function handleNext() {
    // Validar respuesta actual
    if (!validateCurrentResponse()) {
        ClaroVinculo.showError('Por favor responde la pregunta antes de continuar');
        return;
    }
    
    // Guardar respuesta
    const question = currentQuestionnaire.questions[currentQuestionIndex];
    responses[question.id] = getCurrentResponse();
    
    // Avanzar a la siguiente pregunta
    if (currentQuestionIndex < currentQuestionnaire.questions.length - 1) {
        currentQuestionIndex++;
        renderQuestion(currentQuestionIndex);
        updateProgress();
        updateNavigationButtons();
    }
}

function handlePrevious() {
    if (currentQuestionIndex > 0) {
        // Guardar respuesta actual
        const question = currentQuestionnaire.questions[currentQuestionIndex];
        responses[question.id] = getCurrentResponse();
        
        currentQuestionIndex--;
        renderQuestion(currentQuestionIndex);
        updateProgress();
        updateNavigationButtons();
    }
}

async function handleSubmit(e) {
    e.preventDefault();
    
    // Validar respuesta final
    if (!validateCurrentResponse()) {
        ClaroVinculo.showError('Por favor responde la pregunta antes de enviar');
        return;
    }
    
    // Guardar última respuesta
    const question = currentQuestionnaire.questions[currentQuestionIndex];
    responses[question.id] = getCurrentResponse();
    
    // Solicitar nombre del respondiente
    const respondentName = prompt('Por favor, ingresa tu nombre para completar el cuestionario:');
    if (!respondentName || respondentName.trim().length === 0) {
        ClaroVinculo.showError('El nombre es requerido para completar el cuestionario');
        return;
    }
    
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Enviando respuestas...';
        
        const response = await fetch(`${ClaroVinculo.API_BASE}/questionnaire/${invitationId}/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                respondent_name: respondentName.trim(),
                responses: responses
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showCompletionState(result.response_id);
            ClaroVinculo.showSuccess('¡Respuestas enviadas exitosamente!');
        } else {
            throw new Error(result.error || 'Error al enviar las respuestas');
        }
        
    } catch (error) {
        console.error('Error submitting questionnaire:', error);
        ClaroVinculo.showError(error.message);
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

function handleViewResults() {
    // Redirigir a la página de resultados
    window.location.href = `/results/${invitationId}`;
}

function updateProgress() {
    const progress = ((currentQuestionIndex + 1) / currentQuestionnaire.questions.length) * 100;
    document.getElementById('progressFill').style.width = `${progress}%`;
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    // Mostrar/ocultar botón anterior
    prevBtn.style.display = currentQuestionIndex > 0 ? 'inline-block' : 'none';
    
    // Mostrar botón siguiente o enviar
    if (currentQuestionIndex === currentQuestionnaire.questions.length - 1) {
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'inline-block';
    } else {
        nextBtn.style.display = 'inline-block';
        submitBtn.style.display = 'none';
    }
}

function showCompletionState(responseId) {
    document.getElementById('questionnaireHeader').style.display = 'none';
    document.getElementById('progressContainer').style.display = 'none';
    document.getElementById('questionnaireForm').style.display = 'none';
    document.getElementById('completionState').style.display = 'block';
    
    // Guardar response ID para ver resultados
    sessionStorage.setItem('responseId', responseId);
}

function showErrorState(message) {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('errorState').style.display = 'block';
    document.getElementById('errorMessage').textContent = message;
}
