<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClaroVínculo - Redirigiendo...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="spinner"></div>
    <h1>ClaroVíncu<PERSON></h1>
    <p>Redirigiendo a la aplicación...</p>

    <script>
        // Función para redirigir automáticamente
        function redirectToApp() {
            const hash = window.location.hash;

            console.log('URL actual:', window.location.href);
            console.log('Hash detectado:', hash);

            // Si hay un hash con tokens de autenticación, redirigir directamente a la aplicación
            if (hash && hash.includes('access_token')) {
                console.log('Tokens detectados, redirigiendo a aplicación con tokens...');
                // Redirigir a la aplicación principal con los tokens
                window.location.href = '/healthy-love_augment_proove/' + hash;
            } else {
                // Redirección normal a la aplicación
                console.log('Sin tokens, redirigiendo a aplicación...');
                window.location.href = '/healthy-love_augment_proove/';
            }
        }

        // Ejecutar inmediatamente cuando la página carga
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Página de redirección cargada');
            redirectToApp();
        });

        // También ejecutar después de un breve momento como respaldo
        setTimeout(redirectToApp, 500);
    </script>
</body>
</html>
