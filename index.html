<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClaroVínculo - Redirigiendo...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        h1 {
            margin: 0 0 0.5rem 0;
            font-size: 2rem;
        }
        
        p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="spinner"></div>
    <h1>ClaroVínculo</h1>
    <p>Redirigiendo a la aplicación...</p>

    <script>
        // Función para redirigir automáticamente
        function redirectToApp() {
            const currentUrl = window.location.href;
            const hash = window.location.hash;
            
            // Si hay un hash con tokens de autenticación, redirigir a auth-callback
            if (hash && hash.includes('access_token')) {
                window.location.href = '/healthy-love_augment_proove/auth-callback.html' + hash;
            } else {
                // Redirección normal a la aplicación
                window.location.href = '/healthy-love_augment_proove/';
            }
        }
        
        // Redirigir después de un breve momento
        setTimeout(redirectToApp, 1000);
    </script>
</body>
</html>
