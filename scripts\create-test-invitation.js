// Script para crear una invitación de prueba
const fetch = require('node-fetch');

async function createTestInvitation() {
  try {
    console.log('🔄 Creando invitación de prueba...');
    
    const response = await fetch('http://localhost:3000/api/invitations/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sender_name: '<PERSON>',
        recipient_name: '<PERSON>',
        relationship_type: 'nueva_conexion',
        message: '<PERSON><PERSON>, me gustaría conocer mejor tus sentimientos hacia nuestra posible relación. ¿Te gustaría participar en este diálogo honesto?'
      })
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ Invitación creada exitosamente!');
      console.log('🔗 ID de invitación:', result.invitation.id);
      console.log('🌐 Enlace:', result.invitation.link);
      console.log('\n📋 Puedes probar la invitación en:');
      console.log(`   http://localhost:3000/invitation/${result.invitation.id}`);
    } else {
      console.error('❌ Error:', result.error);
    }

  } catch (error) {
    console.error('❌ Error creando invitación:', error.message);
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  createTestInvitation();
}

module.exports = createTestInvitation;
