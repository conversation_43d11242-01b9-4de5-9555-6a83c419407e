const passport = require('passport');
const LocalStrategy = require('passport-local').Strategy;
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const User = require('../models/User');

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findById(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Local Strategy (Email/Password)
passport.use(new LocalStrategy({
  usernameField: 'email',
  passwordField: 'password'
}, async (email, password, done) => {
  try {
    const user = await User.findByEmail(email);
    
    if (!user) {
      return done(null, false, { message: 'Email no registrado' });
    }
    
    const isValidPassword = await user.validatePassword(password);
    
    if (!isValidPassword) {
      return done(null, false, { message: 'Contraseña incorrecta' });
    }
    
    // Update last login
    await user.updateLastLogin();
    
    return done(null, user);
  } catch (error) {
    return done(error);
  }
}));

// Google OAuth Strategy (only if credentials are provided)
if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: "/api/auth/google/callback"
  }, async (accessToken, refreshToken, profile, done) => {
    try {
      // Check if user already exists with this Google ID
      let user = await User.findByGoogleId(profile.id);

      if (user) {
        // Update last login
        await user.updateLastLogin();
        return done(null, user);
      }

      // Check if user exists with same email
      user = await User.findByEmail(profile.emails[0].value);

      if (user) {
        // Link Google account to existing user
        user.google_id = profile.id;
        user.avatar_url = profile.photos[0]?.value;
        user.email_verified = true;
        await user.save();
        await user.updateLastLogin();
        return done(null, user);
      }

      // Create new user
      const newUser = await User.create({
        name: profile.displayName,
        email: profile.emails[0].value,
        google_id: profile.id,
        avatar_url: profile.photos[0]?.value,
        email_verified: true
      });

      await newUser.updateLastLogin();
      return done(null, newUser);

    } catch (error) {
      return done(error, null);
    }
  }));
} else {
  console.log('⚠️  Google OAuth no configurado. Agrega GOOGLE_CLIENT_ID y GOOGLE_CLIENT_SECRET al archivo .env para habilitar login con Google.');
}

module.exports = passport;
