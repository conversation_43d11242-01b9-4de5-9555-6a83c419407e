# 🚀 Instrucciones de Deployment

## 1. Configurar Supabase

1. Ve a [supabase.com](https://supabase.com) y crea un proyecto
2. En el SQL Editor, ejecuta el código de `supabase-setup.md`
3. Ve a Settings > API y copia:
   - Project URL
   - Anon key

## 2. Actualizar Configuración

Edita `js/config.js`:
```javascript
const SUPABASE_CONFIG = {
  url: 'https://tu-proyecto-real.supabase.co',
  anon<PERSON>ey: 'tu-anon-key-real'
};
```

## 3. Subir a GitHub

```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/TU-USUARIO/clarovinculo-app.git
git push -u origin main
```

## 4. Activar GitHub Pages

1. Ve a tu repositorio en GitHub
2. Settings > Pages
3. Source: Deploy from a branch
4. Branch: main, Folder: / (root)
5. Save

## 5. Configurar URLs en Supabase

En tu proyecto de Supabase:
1. Authentication > URL Configuration
2. Site URL: `https://TU-USUARIO.github.io/clarovinculo-app/`
3. Redirect URLs: agregar la misma URL

## 6. ¡Listo! 🎉

Tu app estará disponible en:
`https://TU-USUARIO.github.io/clarovinculo-app/`
