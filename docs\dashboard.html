<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - ClaroVínculo</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-heart"></i>
                    <h1>ClaroVínculo</h1>
                </div>
                <nav class="header-nav" id="headerNav">
                    <!-- Se llenará dinámicamente -->
                </nav>
            </div>
            <div class="user-welcome" id="userWelcome" style="display: none;">
                <p>¡Hola, <span id="userName"></span>! 👋</p>
            </div>
        </header>

        <!-- Dashboard Content -->
        <section class="dashboard-main">
            <div class="dashboard-header">
                <h2>Mi Dashboard</h2>
                <p>Gestiona tus invitaciones y revisa tus conexiones</p>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <button id="createInvitationBtn" class="action-card">
                    <i class="fas fa-plus-circle"></i>
                    <h3>Crear Nueva Invitación</h3>
                    <p>Invita a alguien a un diálogo honesto</p>
                </button>
                
                <div class="action-card stats">
                    <i class="fas fa-chart-line"></i>
                    <h3>Mis Estadísticas</h3>
                    <div class="stats-grid">
                        <div class="stat">
                            <span class="stat-number" id="totalInvitations">0</span>
                            <span class="stat-label">Invitaciones</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="pendingInvitations">0</span>
                            <span class="stat-label">Pendientes</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number" id="completedInvitations">0</span>
                            <span class="stat-label">Completadas</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invitations -->
            <div class="recent-invitations">
                <h3>Mis Invitaciones Recientes</h3>
                <div id="invitationsList" class="invitations-list">
                    <!-- Se llenará dinámicamente -->
                </div>
                
                <div id="noInvitations" class="empty-state" style="display: none;">
                    <i class="fas fa-inbox"></i>
                    <h4>No tienes invitaciones aún</h4>
                    <p>¡Crea tu primera invitación para comenzar!</p>
                    <button class="btn-primary" onclick="showCreateForm()">
                        <i class="fas fa-plus"></i>
                        Crear Primera Invitación
                    </button>
                </div>
            </div>
        </section>

        <!-- Create Invitation Modal -->
        <div id="createModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Crear Nueva Invitación</h3>
                    <button class="close-modal" onclick="hideCreateForm()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="invitationForm" class="invitation-form">
                    <div class="form-group">
                        <label for="recipientName">Nombre de la persona (opcional)</label>
                        <input type="text" id="recipientName" name="recipientName" 
                               placeholder="¿Cómo se llama la persona?">
                    </div>

                    <div class="form-group">
                        <label for="relationshipType">Tipo de relación</label>
                        <select id="relationshipType" name="relationshipType" required>
                            <option value="">Selecciona el tipo de relación</option>
                            <option value="nueva_conexion">Personas que se están conociendo</option>
                            <option value="pareja_establecida">Pareja establecida</option>
                            <option value="amistad">Amistad</option>
                            <option value="familia">Relación familiar</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="customMessage">Mensaje personalizado (opcional)</label>
                        <textarea id="customMessage" name="customMessage" rows="3" 
                                  placeholder="Añade un mensaje personal..."></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="hideCreateForm()">
                            Cancelar
                        </button>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            Crear Invitación
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 ClaroVínculo. Facilitando relaciones auténticas y saludables.</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heart fa-beat"></i>
            <p>Cargando...</p>
        </div>
    </div>

    <script src="js/supabase-client.js"></script>
    
    <script>
        let currentUser = null;

        // Inicializar dashboard
        document.addEventListener('DOMContentLoaded', async function() {
            // Configurar listener de cambios de autenticación
            SupabaseAuth.onAuthStateChange((event, session) => {
                console.log('Cambio de estado de autenticación:', event, session);

                if (event === 'SIGNED_OUT' || !session) {
                    console.log('Usuario desconectado, redirigiendo...');
                    window.location.href = 'login.html';
                } else if (event === 'SIGNED_IN' && session.user) {
                    console.log('Usuario conectado:', session.user.email);
                    currentUser = session.user;
                    updateUI();
                    loadUserData();
                }
            });

            await checkAuthAndLoadDashboard();
            setupEventListeners();
        });

        async function checkAuthAndLoadDashboard() {
            try {
                showLoading(true);

                // Esperar un momento para que Supabase inicialice
                await new Promise(resolve => setTimeout(resolve, 1000));

                const { success, user } = await SupabaseAuth.getCurrentUser();

                console.log('Verificación de autenticación:', { success, user });

                if (success && user) {
                    console.log('Usuario autenticado correctamente:', user.email);
                    currentUser = user;
                    updateUI();
                    await loadUserData();
                } else {
                    console.log('Usuario no autenticado, esperando...');
                    // No redirigir inmediatamente, el listener se encargará
                    showLoading(false);

                    // Solo redirigir después de un tiempo considerable
                    setTimeout(() => {
                        if (!currentUser) {
                            console.log('Timeout: redirigiendo al login...');
                            window.location.href = 'login.html';
                        }
                    }, 3000);
                }

            } catch (error) {
                console.error('Error verificando autenticación:', error);
                showLoading(false);

                setTimeout(() => {
                    if (!currentUser) {
                        window.location.href = 'login.html';
                    }
                }, 3000);
            }
        }

        function updateUI() {
            // Actualizar header
            const headerNav = document.getElementById('headerNav');
            const userWelcome = document.getElementById('userWelcome');
            const userName = document.getElementById('userName');

            if (currentUser) {
                // Mostrar nombre del usuario
                userName.textContent = currentUser.user_metadata?.full_name || currentUser.email;
                userWelcome.style.display = 'block';

                // Actualizar navegación
                headerNav.innerHTML = `
                    <a href="index.html" class="nav-link">Inicio</a>
                    <button onclick="handleLogout()" class="nav-link btn-outline">
                        <i class="fas fa-sign-out-alt"></i>
                        Cerrar Sesión
                    </button>
                `;
            }
        }

        async function loadUserData() {
            if (!currentUser) {
                console.log('No hay usuario para cargar datos');
                return;
            }

            try {
                showLoading(true);

                // Cargar invitaciones del usuario
                const { success, invitations } = await SupabaseDB.getUserInvitations(currentUser.id);

                if (success) {
                    updateStats(invitations);
                    displayInvitations(invitations);
                } else {
                    console.error('Error cargando invitaciones');
                    // Mostrar estado vacío en caso de error
                    updateStats([]);
                    displayInvitations([]);
                }

            } catch (error) {
                console.error('Error cargando datos:', error);
                // Mostrar estado vacío en caso de error
                updateStats([]);
                displayInvitations([]);
            } finally {
                showLoading(false);
            }
        }

        function updateStats(invitations) {
            const total = invitations.length;
            const pending = invitations.filter(inv => inv.status === 'pending').length;
            const completed = invitations.filter(inv => inv.status === 'completed').length;

            document.getElementById('totalInvitations').textContent = total;
            document.getElementById('pendingInvitations').textContent = pending;
            document.getElementById('completedInvitations').textContent = completed;
        }

        function displayInvitations(invitations) {
            const listContainer = document.getElementById('invitationsList');
            const noInvitations = document.getElementById('noInvitations');

            if (invitations.length === 0) {
                listContainer.style.display = 'none';
                noInvitations.style.display = 'block';
                return;
            }

            listContainer.style.display = 'block';
            noInvitations.style.display = 'none';

            listContainer.innerHTML = invitations.map(invitation => `
                <div class="invitation-card">
                    <div class="invitation-info">
                        <h4>${invitation.recipient_name || 'Invitación anónima'}</h4>
                        <p class="invitation-type">${getRelationshipTypeLabel(invitation.relationship_type)}</p>
                        <p class="invitation-date">Creada: ${new Date(invitation.created_at).toLocaleDateString()}</p>
                    </div>
                    <div class="invitation-status">
                        <span class="status-badge ${invitation.status}">${getStatusLabel(invitation.status)}</span>
                        <div class="invitation-actions">
                            <button onclick="copyInvitationLink('${invitation.id}')" class="btn-icon" title="Copiar enlace">
                                <i class="fas fa-copy"></i>
                            </button>
                            ${invitation.status === 'completed' ? 
                                `<button onclick="viewResults('${invitation.id}')" class="btn-icon" title="Ver resultados">
                                    <i class="fas fa-eye"></i>
                                </button>` : ''
                            }
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function setupEventListeners() {
            document.getElementById('createInvitationBtn').addEventListener('click', showCreateForm);
            document.getElementById('invitationForm').addEventListener('submit', handleCreateInvitation);
        }

        function showCreateForm() {
            document.getElementById('createModal').style.display = 'flex';
        }

        function hideCreateForm() {
            document.getElementById('createModal').style.display = 'none';
            document.getElementById('invitationForm').reset();
        }

        async function handleCreateInvitation(e) {
            e.preventDefault();
            
            try {
                showLoading(true);
                
                const formData = new FormData(e.target);
                const invitationData = {
                    sender_id: currentUser.id,
                    sender_name: currentUser.user_metadata?.full_name || currentUser.email,
                    recipient_name: formData.get('recipientName') || null,
                    relationship_type: formData.get('relationshipType'),
                    custom_message: formData.get('customMessage') || null,
                    status: 'pending'
                };

                const { success, invitation } = await SupabaseDB.createInvitation(invitationData);
                
                if (success) {
                    hideCreateForm();
                    await loadUserData(); // Recargar datos
                    
                    // Mostrar enlace de la invitación
                    const invitationUrl = `${window.location.origin}/invitation.html?id=${invitation.id}`;
                    
                    alert(`¡Invitación creada! Enlace: ${invitationUrl}`);
                } else {
                    throw new Error('Error creando invitación');
                }
                
            } catch (error) {
                console.error('Error:', error);
                alert('Error creando la invitación. Inténtalo de nuevo.');
            } finally {
                showLoading(false);
            }
        }

        async function handleLogout() {
            try {
                const { success } = await SupabaseAuth.signOut();
                if (success) {
                    window.location.href = 'index.html';
                }
            } catch (error) {
                console.error('Error cerrando sesión:', error);
            }
        }

        function copyInvitationLink(invitationId) {
            const url = `${window.location.origin}/invitation.html?id=${invitationId}`;
            navigator.clipboard.writeText(url).then(() => {
                alert('¡Enlace copiado al portapapeles!');
            });
        }

        function viewResults(invitationId) {
            window.location.href = `results.html?id=${invitationId}`;
        }

        function getRelationshipTypeLabel(type) {
            const labels = {
                'nueva_conexion': 'Personas que se están conociendo',
                'pareja_establecida': 'Pareja establecida',
                'amistad': 'Amistad',
                'familia': 'Relación familiar'
            };
            return labels[type] || type;
        }

        function getStatusLabel(status) {
            const labels = {
                'pending': 'Pendiente',
                'accepted': 'Aceptada',
                'declined': 'Rechazada',
                'completed': 'Completada'
            };
            return labels[status] || status;
        }

        function showLoading(show) {
            document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        }
    </script>
</body>
</html>
