// Invitation page functionality
let currentInvitation = null;
const API_BASE = '/api';

document.addEventListener('DOMContentLoaded', function() {
    initializeInvitationPage();
});

function initializeInvitationPage() {
    // Obtener ID de la invitación de la URL
    const pathParts = window.location.pathname.split('/');
    const invitationId = pathParts[pathParts.length - 1];
    
    if (!invitationId) {
        showErrorState('ID de invitación no válido');
        return;
    }
    
    loadInvitation(invitationId);
    setupEventListeners();
}

async function loadInvitation(invitationId) {
    try {
        const response = await fetch(`${API_BASE}/invitations/${invitationId}`);
        const result = await response.json();
        
        if (result.success) {
            currentInvitation = result.invitation;
            displayInvitation(result.invitation);
        } else {
            throw new Error(result.error || 'Invitación no encontrada');
        }
        
    } catch (error) {
        console.error('Error loading invitation:', error);
        showErrorState(error.message);
    }
}

function displayInvitation(invitation) {
    // Ocultar loading y mostrar contenido
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('invitationContent').style.display = 'block';
    
    // Llenar datos de la invitación
    document.getElementById('invitationTitle').textContent = 
        `${invitation.sender_name} te ha invitado a un diálogo`;
    
    const messageElement = document.getElementById('invitationMessage');
    messageElement.innerHTML = `
        <p>${invitation.message}</p>
        <p style="margin-top: 1rem; font-style: italic; color: #667eea;">
            "Este proceso toma solo unos minutos y significa mucho para ${invitation.sender_name}. 
            ¿Te gustaría participar?"
        </p>
    `;
    
    // Mostrar tipo de relación de forma amigable
    const relationshipTypes = {
        'nueva_conexion': 'Personas que se están conociendo',
        'pareja_establecida': 'Pareja establecida',
        'amistad': 'Amistad',
        'familia': 'Relación familiar'
    };
    
    document.getElementById('relationshipType').textContent = 
        relationshipTypes[invitation.relationship_type] || invitation.relationship_type;
    
    // Verificar si ya fue respondida
    if (invitation.status !== 'pending') {
        showAlreadyRespondedState(invitation.status);
    }
}

function setupEventListeners() {
    // Botón aceptar
    document.getElementById('acceptBtn').addEventListener('click', handleAcceptInvitation);
    
    // Botón rechazar
    document.getElementById('declineBtn').addEventListener('click', handleDeclineInvitation);
    
    // Botón enviar rechazo
    document.getElementById('sendDeclineBtn').addEventListener('click', handleSendDecline);
    
    // Botón comenzar cuestionario
    document.getElementById('startQuestionnaireBtn').addEventListener('click', handleStartQuestionnaire);
}

async function handleAcceptInvitation() {
    if (!currentInvitation) return;
    
    try {
        showLoading(true);
        document.getElementById('loadingText').textContent = 'Aceptando invitación...';

        const response = await fetch(`${API_BASE}/invitations/${currentInvitation.id}/accept`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showAcceptedState();
            showSuccess('¡Invitación aceptada exitosamente!');
        } else {
            throw new Error(result.error || 'Error al aceptar la invitación');
        }

    } catch (error) {
        console.error('Error accepting invitation:', error);
        showError(error.message);
    } finally {
        showLoading(false);
    }
}

function handleDeclineInvitation() {
    // Mostrar estado de rechazo
    document.getElementById('invitationContent').style.display = 'none';
    document.getElementById('declinedState').style.display = 'block';
}

async function handleSendDecline() {
    if (!currentInvitation) return;
    
    try {
        showLoading(true);
        document.getElementById('loadingText').textContent = 'Enviando respuesta...';

        const reason = document.getElementById('declineReason').value;

        const response = await fetch(`${API_BASE}/invitations/${currentInvitation.id}/decline`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reason })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('Respuesta enviada. Gracias por tu honestidad.');

            // Mostrar mensaje final
            document.getElementById('declinedState').innerHTML = `
                <i class="fas fa-check-circle" style="font-size: 3rem; color: #48bb78; margin-bottom: 1rem;"></i>
                <h3>Respuesta enviada</h3>
                <p>Gracias por tomarte el tiempo de responder. Tu honestidad es valiosa y respetamos completamente tu decisión.</p>
                <a href="/" class="btn-secondary" style="margin-top: 1.5rem; display: inline-block; text-decoration: none;">
                    Ir a inicio
                </a>
            `;
        } else {
            throw new Error(result.error || 'Error al enviar la respuesta');
        }

    } catch (error) {
        console.error('Error declining invitation:', error);
        showError(error.message);
    } finally {
        showLoading(false);
    }
}

function handleStartQuestionnaire() {
    if (!currentInvitation) return;
    
    // Redirigir al cuestionario
    window.location.href = `/questionnaire/${currentInvitation.id}`;
}

function showAcceptedState() {
    document.getElementById('invitationContent').style.display = 'none';
    document.getElementById('acceptedState').style.display = 'block';
}

function showErrorState(message) {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('errorState').style.display = 'block';
    
    // Personalizar mensaje de error si es necesario
    const errorText = document.querySelector('#errorState p');
    if (message && message !== 'Invitación no encontrada') {
        errorText.textContent = message;
    }
}

function showAlreadyRespondedState(status) {
    document.getElementById('invitationContent').innerHTML = `
        <i class="fas fa-info-circle" style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;"></i>
        <h3>Invitación ya respondida</h3>
        <p>Esta invitación ya ha sido ${status === 'accepted' ? 'aceptada' : 'rechazada'} anteriormente.</p>
        ${status === 'accepted' ? `
            <button onclick="window.location.href='/questionnaire/${currentInvitation.id}'" class="btn-primary" style="margin-top: 1.5rem;">
                <i class="fas fa-arrow-right"></i>
                Ir al cuestionario
            </button>
        ` : ''}
        <a href="/" class="btn-secondary" style="margin-top: 1rem; display: inline-block; text-decoration: none;">
            Crear nueva invitación
        </a>
    `;
}

// Utility functions
function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = show ? 'flex' : 'none';
    }
}

function showError(message) {
    showNotification(message, 'error');
}

function showSuccess(message) {
    showNotification(message, 'success');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Inline styles for notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#fed7d7' : type === 'success' ? '#c6f6d5' : '#bee3f8'};
        color: ${type === 'error' ? '#c53030' : type === 'success' ? '#2f855a' : '#2b6cb0'};
        padding: 1rem 1.5rem;
        border-radius: 12px;
        border: 1px solid ${type === 'error' ? '#feb2b2' : type === 'success' ? '#9ae6b4' : '#90cdf4'};
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 1001;
        display: flex;
        align-items: center;
        gap: 1rem;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }
        .notification-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .notification-close:hover {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);

    // Add to DOM
    document.body.appendChild(notification);

    // Setup close button
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });

    // Auto-close after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
