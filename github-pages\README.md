# ClaroVínculo - Aplicación Web

Una aplicación para facilitar conexiones auténticas y relaciones saludables.

## 🚀 Tecnologías

- **Frontend**: HTML, CSS, JavaScript vanilla
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Hosting**: GitHub Pages
- **Autenticación**: Supabase Auth (Google OAuth + Email)

## 🔧 Configuración

1. **Configurar Supabase**:
   - Crear proyecto en [supabase.com](https://supabase.com)
   - Ejecutar el SQL del archivo `supabase-setup.md`
   - Configurar autenticación

2. **Actualizar configuración**:
   - Editar `js/config.js` con tus credenciales de Supabase
   - Actualizar URLs en `js/supabase-client.js`

3. **Configurar GitHub Pages**:
   - Activar GitHub Pages en Settings > Pages
   - Source: Deploy from a branch (main)

## 📱 Funcionalidades

- ✅ Crear invitaciones personalizadas
- ✅ Autenticación con email y Google
- ✅ Dashboard de usuario
- ✅ Cuestionarios interactivos
- ✅ Análisis de compatibilidad
- ✅ Compartir por WhatsApp, Telegram, Email

## 🌐 Demo

[Ver aplicación en vivo](https://tu-usuario.github.io/clarovinculo-app)

## 📄 Licencia

MIT License
