<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Invitación - ClaroVínculo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            color: #667eea;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #5a67d8;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f7fafc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">❤️ ClaroVínculo</div>
            <h2>Test de Invitación</h2>
        </div>
        
        <div>
            <p><strong>ID de Invitación:</strong> <span id="invitationId">a6a21ebe-de0a-4621-9b0e-1a58f8dbb340</span></p>
            
            <button class="btn" onclick="testLoadInvitation()">Cargar Invitación</button>
            <button class="btn" onclick="testAcceptInvitation()">Aceptar Invitación</button>
            <button class="btn" onclick="testDeclineInvitation()">Rechazar Invitación</button>
            
            <div id="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        const INVITATION_ID = 'a6a21ebe-de0a-4621-9b0e-1a58f8dbb340';

        async function testLoadInvitation() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 Cargando invitación...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/invitations/${INVITATION_ID}`);
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <h3>✅ Invitación cargada exitosamente</h3>
                        <p><strong>Remitente:</strong> ${result.invitation.sender_name}</p>
                        <p><strong>Destinatario:</strong> ${result.invitation.recipient_name || 'No especificado'}</p>
                        <p><strong>Tipo:</strong> ${result.invitation.relationship_type}</p>
                        <p><strong>Estado:</strong> ${result.invitation.status}</p>
                        <p><strong>Mensaje:</strong> ${result.invitation.message}</p>
                        <p><strong>Creada:</strong> ${result.invitation.created_at}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<p>❌ Error: ${result.error}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ Error de conexión: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }

        async function testAcceptInvitation() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 Aceptando invitación...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/invitations/${INVITATION_ID}/accept`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <h3>✅ Invitación aceptada</h3>
                        <p>${result.message}</p>
                        <p><strong>Enlace al cuestionario:</strong> <a href="${result.questionnaire_link}" target="_blank">${result.questionnaire_link}</a></p>
                    `;
                } else {
                    resultDiv.innerHTML = `<p>❌ Error: ${result.error}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ Error de conexión: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }

        async function testDeclineInvitation() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 Rechazando invitación...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/invitations/${INVITATION_ID}/decline`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ reason: 'Prueba de funcionalidad' })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <h3>✅ Invitación rechazada</h3>
                        <p>${result.message}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `<p>❌ Error: ${result.error}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p>❌ Error de conexión: ${error.message}</p>`;
                console.error('Error:', error);
            }
        }

        // Auto-cargar la invitación al cargar la página
        window.addEventListener('load', testLoadInvitation);
    </script>
</body>
</html>
