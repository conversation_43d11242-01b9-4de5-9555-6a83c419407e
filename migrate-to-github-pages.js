#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Migrando ClaroVínculo a GitHub Pages + Supabase...\n');

// Crear estructura de directorios para GitHub Pages
const createDirectories = () => {
    const dirs = ['github-pages', 'github-pages/js', 'github-pages/styles'];
    
    dirs.forEach(dir => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            console.log(`✅ Creado directorio: ${dir}`);
        }
    });
};

// Copiar archivos CSS
const copyStyles = () => {
    console.log('\n📄 Copiando archivos CSS...');
    
    const sourceCSS = 'public/styles/main.css';
    const targetCSS = 'github-pages/styles/main.css';
    
    if (fs.existsSync(sourceCSS)) {
        fs.copyFileSync(sourceCSS, targetCSS);
        console.log(`✅ Copiado: ${sourceCSS} → ${targetCSS}`);
    } else {
        console.log(`⚠️  No encontrado: ${sourceCSS}`);
    }
};

// Copiar y renombrar archivos HTML
const copyHTMLFiles = () => {
    console.log('\n📄 Copiando archivos HTML...');
    
    const htmlFiles = [
        { source: 'public/index.html', target: 'github-pages/index.html' },
        { source: 'public/invitation-supabase.html', target: 'github-pages/invitation.html' },
        { source: 'public/login-supabase.html', target: 'github-pages/login.html' },
        { source: 'public/register-supabase.html', target: 'github-pages/register.html' }
    ];
    
    htmlFiles.forEach(({ source, target }) => {
        if (fs.existsSync(source)) {
            fs.copyFileSync(source, target);
            console.log(`✅ Copiado: ${source} → ${target}`);
        } else {
            console.log(`⚠️  No encontrado: ${source}`);
        }
    });
};

// Copiar archivos JavaScript
const copyJSFiles = () => {
    console.log('\n📄 Copiando archivos JavaScript...');
    
    const jsFiles = [
        'public/js/supabase-client.js',
        'public/js/app-supabase.js'
    ];
    
    jsFiles.forEach(file => {
        const fileName = path.basename(file);
        const target = `github-pages/js/${fileName}`;
        
        if (fs.existsSync(file)) {
            fs.copyFileSync(file, target);
            console.log(`✅ Copiado: ${file} → ${target}`);
        } else {
            console.log(`⚠️  No encontrado: ${file}`);
        }
    });
};

// Crear archivo de configuración
const createConfigFile = () => {
    console.log('\n⚙️  Creando archivo de configuración...');
    
    const configContent = `// Configuración para GitHub Pages + Supabase
const SUPABASE_CONFIG = {
  url: 'https://TU-PROYECTO-ID.supabase.co', // ⚠️ CAMBIAR ESTO
  anonKey: 'TU-ANON-KEY-AQUI' // ⚠️ CAMBIAR ESTO
};

// Configuración de la aplicación
const APP_CONFIG = {
  environment: 'production',
  appUrl: 'https://TU-USUARIO.github.io/clarovinculo-app', // ⚠️ CAMBIAR ESTO
  version: '1.0.0'
};

// Exportar configuración
window.SUPABASE_CONFIG = SUPABASE_CONFIG;
window.APP_CONFIG = APP_CONFIG;
`;
    
    fs.writeFileSync('github-pages/js/config.js', configContent);
    console.log('✅ Creado: github-pages/js/config.js');
};

// Crear README para GitHub Pages
const createReadme = () => {
    console.log('\n📝 Creando README...');
    
    const readmeContent = `# ClaroVínculo - Aplicación Web

Una aplicación para facilitar conexiones auténticas y relaciones saludables.

## 🚀 Tecnologías

- **Frontend**: HTML, CSS, JavaScript vanilla
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Hosting**: GitHub Pages
- **Autenticación**: Supabase Auth (Google OAuth + Email)

## 🔧 Configuración

1. **Configurar Supabase**:
   - Crear proyecto en [supabase.com](https://supabase.com)
   - Ejecutar el SQL del archivo \`supabase-setup.md\`
   - Configurar autenticación

2. **Actualizar configuración**:
   - Editar \`js/config.js\` con tus credenciales de Supabase
   - Actualizar URLs en \`js/supabase-client.js\`

3. **Configurar GitHub Pages**:
   - Activar GitHub Pages en Settings > Pages
   - Source: Deploy from a branch (main)

## 📱 Funcionalidades

- ✅ Crear invitaciones personalizadas
- ✅ Autenticación con email y Google
- ✅ Dashboard de usuario
- ✅ Cuestionarios interactivos
- ✅ Análisis de compatibilidad
- ✅ Compartir por WhatsApp, Telegram, Email

## 🌐 Demo

[Ver aplicación en vivo](https://tu-usuario.github.io/clarovinculo-app)

## 📄 Licencia

MIT License
`;
    
    fs.writeFileSync('github-pages/README.md', readmeContent);
    console.log('✅ Creado: github-pages/README.md');
};

// Crear archivo .gitignore
const createGitignore = () => {
    console.log('\n🚫 Creando .gitignore...');
    
    const gitignoreContent = `# Dependencies
node_modules/

# Environment variables
.env
.env.local
.env.production

# Logs
*.log

# OS generated files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp
`;
    
    fs.writeFileSync('github-pages/.gitignore', gitignoreContent);
    console.log('✅ Creado: github-pages/.gitignore');
};

// Crear instrucciones de deployment
const createDeployInstructions = () => {
    console.log('\n📋 Creando instrucciones de deployment...');
    
    const instructionsContent = `# 🚀 Instrucciones de Deployment

## 1. Configurar Supabase

1. Ve a [supabase.com](https://supabase.com) y crea un proyecto
2. En el SQL Editor, ejecuta el código de \`supabase-setup.md\`
3. Ve a Settings > API y copia:
   - Project URL
   - Anon key

## 2. Actualizar Configuración

Edita \`js/config.js\`:
\`\`\`javascript
const SUPABASE_CONFIG = {
  url: 'https://tu-proyecto-real.supabase.co',
  anonKey: 'tu-anon-key-real'
};
\`\`\`

## 3. Subir a GitHub

\`\`\`bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/TU-USUARIO/clarovinculo-app.git
git push -u origin main
\`\`\`

## 4. Activar GitHub Pages

1. Ve a tu repositorio en GitHub
2. Settings > Pages
3. Source: Deploy from a branch
4. Branch: main, Folder: / (root)
5. Save

## 5. Configurar URLs en Supabase

En tu proyecto de Supabase:
1. Authentication > URL Configuration
2. Site URL: \`https://TU-USUARIO.github.io/clarovinculo-app/\`
3. Redirect URLs: agregar la misma URL

## 6. ¡Listo! 🎉

Tu app estará disponible en:
\`https://TU-USUARIO.github.io/clarovinculo-app/\`
`;
    
    fs.writeFileSync('github-pages/DEPLOY.md', instructionsContent);
    console.log('✅ Creado: github-pages/DEPLOY.md');
};

// Ejecutar migración
const runMigration = () => {
    try {
        createDirectories();
        copyStyles();
        copyHTMLFiles();
        copyJSFiles();
        createConfigFile();
        createReadme();
        createGitignore();
        createDeployInstructions();
        
        console.log('\n🎉 ¡Migración completada exitosamente!');
        console.log('\n📁 Archivos creados en: ./github-pages/');
        console.log('\n📋 Próximos pasos:');
        console.log('1. Configurar Supabase (ver supabase-setup.md)');
        console.log('2. Editar github-pages/js/config.js con tus credenciales');
        console.log('3. Subir a GitHub y activar GitHub Pages');
        console.log('4. Ver DEPLOY.md para instrucciones detalladas');
        
    } catch (error) {
        console.error('❌ Error durante la migración:', error.message);
    }
};

// Ejecutar si se llama directamente
if (require.main === module) {
    runMigration();
}

module.exports = { runMigration };
