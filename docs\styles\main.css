/* Reset y configuración base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #2d3748;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #ff6b9d;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
}

.header-nav {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
}

.nav-link.btn-outline {
    border: 2px solid white;
    padding: 6px 16px;
}

.nav-link.btn-outline:hover {
    background: white;
    color: #667eea;
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Hero Section */
.hero {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.hero-content h2 {
    font-size: 2.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
    text-align: center;
}

.hero-content p {
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 2rem;
    color: #4a5568;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.feature i {
    font-size: 1.5rem;
    color: #667eea;
}

.feature span {
    font-weight: 500;
    color: #2d3748;
}

/* Form Styles */
.create-invitation {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.form-container h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
    text-align: center;
}

.form-container p {
    text-align: center;
    color: #4a5568;
    margin-bottom: 2rem;
}

.invitation-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Buttons */
.btn-primary {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #667eea;
    color: white;
}

/* Result Section */
.result-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    text-align: center;
}

.success-icon i {
    font-size: 4rem;
    color: #48bb78;
    margin-bottom: 1rem;
}

.result-container h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
}

.link-container {
    display: flex;
    gap: 10px;
    margin: 2rem 0;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.link-container input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 0.9rem;
    background: #f7fafc;
}

.btn-copy {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-copy:hover {
    background: #5a67d8;
}

.share-options {
    margin: 2rem 0;
}

.share-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.btn-share {
    padding: 10px 20px;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-share.whatsapp {
    background: #25d366;
    color: white;
}

.btn-share.email {
    background: #ea4335;
    color: white;
}

.btn-share.telegram {
    background: #0088cc;
    color: white;
}

.btn-share:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* How it works */
.how-it-works {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.how-it-works h3 {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: #2d3748;
}

.steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.step {
    text-align: center;
    padding: 1.5rem;
}

.step-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 auto 1rem auto;
}

.step h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.step p {
    color: #4a5568;
    line-height: 1.6;
}

/* Footer */
.footer {
    text-align: center;
    padding: 2rem 0;
    color: rgba(255, 255, 255, 0.8);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-links {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.loading-spinner i {
    font-size: 3rem;
    color: #ff6b9d;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .hero,
    .create-invitation,
    .how-it-works {
        padding: 2rem 1.5rem;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .logo h1 {
        font-size: 2rem;
    }

    .link-container {
        flex-direction: column;
    }

    .share-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-share {
        width: 200px;
        justify-content: center;
    }
}

/* Invitation Page Styles */
.invitation-page {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
}

.invitation-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.invitation-avatar {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto;
    font-size: 2rem;
    color: white;
}

.invitation-message {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: #4a5568;
}

.invitation-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-accept {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-decline {
    background: transparent;
    color: #e53e3e;
    border: 2px solid #e53e3e;
    padding: 12px 26px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-accept:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
}

.btn-decline:hover {
    background: #e53e3e;
    color: white;
}

/* Questionnaire Styles */
.questionnaire-container {
    max-width: 700px;
    margin: 0 auto;
    padding: 2rem;
}

.questionnaire-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.questionnaire-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.question {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.question:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.question-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2d3748;
}

.scale-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.scale-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #4a5568;
    margin-top: 0.5rem;
}

.scale-input {
    flex: 1;
}

.scale-value {
    font-weight: 600;
    color: #667eea;
    min-width: 30px;
    text-align: center;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border-radius: 8px;
    transition: background-color 0.3s ease;
    cursor: pointer;
}

.radio-option:hover {
    background: rgba(102, 126, 234, 0.05);
}

.radio-option input[type="radio"] {
    margin: 0;
}

.radio-option label {
    margin: 0;
    cursor: pointer;
    flex: 1;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    border-radius: 3px;
}

/* Results Page Styles */
.results-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.results-header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.analysis-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.analysis-section h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.insights-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #4a5568;
    margin-bottom: 1.5rem;
}

.recommendations-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #4a5568;
    background: rgba(102, 126, 234, 0.05);
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.indicator {
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
}

.indicator.positive {
    background: rgba(72, 187, 120, 0.1);
    border: 1px solid rgba(72, 187, 120, 0.3);
    color: #2f855a;
}

.indicator.warning {
    background: rgba(237, 137, 54, 0.1);
    border: 1px solid rgba(237, 137, 54, 0.3);
    color: #c05621;
}

.indicator.negative {
    background: rgba(229, 62, 62, 0.1);
    border: 1px solid rgba(229, 62, 62, 0.3);
    color: #c53030;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #4a5568;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f7fafc;
    color: #2d3748;
}

.modal-body {
    padding: 1rem 2rem 2rem 2rem;
}

.analysis-meta {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #4a5568;
}

.analysis-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .analysis-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .action-buttons button {
        width: 100%;
        max-width: 250px;
    }
}

/* Authentication Pages Styles */
.auth-section {
    max-width: 500px;
    margin: 0 auto;
    padding: 2rem;
}

.auth-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.auth-header p {
    color: #4a5568;
    font-size: 1rem;
}

.btn-google {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    width: 100%;
    padding: 14px 20px;
    background: #4285f4;
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.btn-google:hover {
    background: #3367d6;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(66, 133, 244, 0.3);
}

.auth-divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #4a5568;
    font-size: 0.9rem;
}

.auth-form {
    margin-bottom: 2rem;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #4a5568;
    cursor: pointer;
    padding: 4px;
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.8rem;
    color: #4a5568;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1.5rem 0;
    flex-wrap: wrap;
    gap: 1rem;
}

.forgot-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
}

.forgot-link:hover {
    text-decoration: underline;
}

.auth-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.auth-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Features Preview */
.features-preview, .benefits-section {
    margin: 3rem 0;
}

.features-preview h3, .benefits-section h3 {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: white;
}

.features-grid, .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.feature-card, .benefit-item {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.feature-card i, .benefit-item i {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card h4, .benefit-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2d3748;
}

.feature-card p, .benefit-item p {
    color: #4a5568;
    line-height: 1.5;
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
    background: #f7fafc;
}

.sidebar {
    width: 280px;
    background: white;
    border-right: 1px solid #e2e8f0;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 2rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.sidebar-header .logo i {
    font-size: 1.8rem;
    color: #ff6b9d;
}

.sidebar-header .logo h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 1.5rem;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-item a:hover {
    background: rgba(102, 126, 234, 0.05);
    color: #667eea;
}

.nav-item.active a {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border-right-color: #667eea;
}

.nav-item i {
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
    padding: 12px;
    background: #f7fafc;
    border-radius: 12px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.user-details h4 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #2d3748;
}

.user-details p {
    margin: 0;
    font-size: 0.8rem;
    color: #4a5568;
}

.btn-logout {
    width: 100%;
    padding: 10px;
    background: transparent;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-logout:hover {
    background: #fed7d7;
    border-color: #feb2b2;
    color: #c53030;
}

.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
}

.dashboard-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #4a5568;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
}

.sidebar-toggle:hover {
    background: #f7fafc;
}

.dashboard-header h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
}

.content-section {
    display: none;
    padding: 2rem;
    flex: 1;
}

.content-section.active {
    display: block;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
}

.stat-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
}

.stat-content p {
    margin: 0;
    color: #4a5568;
    font-size: 0.9rem;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.dashboard-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dashboard-card h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 1rem;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
}

.quick-action-btn:hover {
    background: rgba(102, 126, 234, 0.05);
    border-color: #667eea;
}

.quick-action-btn i {
    font-size: 1.2rem;
    color: #667eea;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
    color: #2d3748;
}

/* Activity List */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    font-size: 0.9rem;
    color: #2d3748;
}

.activity-time {
    font-size: 0.8rem;
    color: #4a5568;
}

/* Filters */
.filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.filters select {
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    font-size: 0.9rem;
}

/* Invitation Cards */
.invitations-list, .responses-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.invitation-card, .response-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.invitation-header, .response-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.invitation-header h4, .response-header h4 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2d3748;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.pending {
    background: rgba(237, 137, 54, 0.1);
    color: #c05621;
}

.status-badge.accepted {
    background: rgba(72, 187, 120, 0.1);
    color: #2f855a;
}

.status-badge.declined {
    background: rgba(229, 62, 62, 0.1);
    color: #c53030;
}

.response-date {
    font-size: 0.9rem;
    color: #4a5568;
}

.invitation-actions, .response-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.analytics-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.analytics-card h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

.chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border-radius: 12px;
    color: #4a5568;
    font-style: italic;
}

/* Profile */
.profile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.profile-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.profile-card h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-visible {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .profile-grid {
        grid-template-columns: 1fr;
    }

    .invitation-actions, .response-actions {
        flex-direction: column;
    }

    .filters {
        flex-direction: column;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .auth-container {
        padding: 2rem 1.5rem;
    }

    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }
}
