<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrarse - ClaroVínculo</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-heart"></i>
                <h1>ClaroVínculo</h1>
            </div>
            <p class="tagline">Facilitando conexiones auténticas y relaciones saludables</p>
        </header>

        <!-- Register Form -->
        <div class="auth-container">
            <div class="auth-card">
                <h2>Crear Cuenta</h2>
                <p class="auth-subtitle">Únete a ClaroVínculo para gestionar tus invitaciones</p>

                <form id="registerForm" class="auth-form">
                    <div class="form-group">
                        <label for="fullName">Nombre Completo</label>
                        <input type="text" id="fullName" name="fullName" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Correo Electrónico</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Contraseña</label>
                        <input type="password" id="password" name="password" required minlength="6">
                        <small>Mínimo 6 caracteres</small>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirmar Contraseña</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" required>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="acceptTerms" name="acceptTerms" required>
                            <span class="checkmark"></span>
                            Acepto los <a href="#" target="_blank">términos y condiciones</a>
                        </label>
                    </div>

                    <button type="submit" id="registerBtn" class="btn-primary">
                        <i class="fas fa-user-plus"></i>
                        Crear Cuenta
                    </button>
                </form>

                <div class="auth-divider">
                    <span>o</span>
                </div>

                <button id="googleRegisterBtn" class="btn-google">
                    <i class="fab fa-google"></i>
                    Registrarse con Google
                </button>

                <div class="auth-links">
                    <p>¿Ya tienes cuenta? <a href="login-supabase.html">Inicia sesión aquí</a></p>
                    <p><a href="index.html">Volver al inicio</a></p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 ClaroVínculo. Facilitando relaciones auténticas y saludables.</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heart fa-beat"></i>
            <p id="loadingText">Creando cuenta...</p>
        </div>
    </div>

    <script src="js/supabase-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeRegisterPage();
        });

        function initializeRegisterPage() {
            // Verificar si ya está autenticado
            checkAuthState();
            
            // Event listeners
            document.getElementById('registerForm').addEventListener('submit', handleRegister);
            document.getElementById('googleRegisterBtn').addEventListener('click', handleGoogleRegister);
        }

        async function checkAuthState() {
            const { success, user } = await SupabaseAuth.getCurrentUser();
            if (success && user) {
                // Ya está autenticado, redirigir al dashboard
                window.location.href = 'dashboard-supabase.html';
            }
        }

        async function handleRegister(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const fullName = formData.get('fullName');
            const email = formData.get('email');
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            const acceptTerms = formData.get('acceptTerms');

            // Validaciones
            if (!fullName || !email || !password || !confirmPassword) {
                showError('Por favor completa todos los campos');
                return;
            }

            if (password !== confirmPassword) {
                showError('Las contraseñas no coinciden');
                return;
            }

            if (password.length < 6) {
                showError('La contraseña debe tener al menos 6 caracteres');
                return;
            }

            if (!acceptTerms) {
                showError('Debes aceptar los términos y condiciones');
                return;
            }

            try {
                showLoading(true);
                
                const result = await SupabaseAuth.signUp(email, password, {
                    full_name: fullName
                });
                
                if (result.success) {
                    showSuccess('¡Cuenta creada exitosamente! Revisa tu email para confirmar tu cuenta.');
                    setTimeout(() => {
                        window.location.href = 'login-supabase.html';
                    }, 2000);
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('Error:', error);
                showError(error.message || 'Error al crear la cuenta');
            } finally {
                showLoading(false);
            }
        }

        async function handleGoogleRegister() {
            try {
                showLoading(true);
                
                const result = await SupabaseAuth.signInWithGoogle();
                
                if (result.success) {
                    showSuccess('Redirigiendo a Google...');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('Error:', error);
                showError(error.message || 'Error al registrarse con Google');
                showLoading(false);
            }
        }

        function showLoading(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const registerBtn = document.getElementById('registerBtn');
            const googleBtn = document.getElementById('googleRegisterBtn');
            
            if (loadingOverlay) {
                loadingOverlay.style.display = show ? 'flex' : 'none';
            }
            
            if (registerBtn) {
                registerBtn.disabled = show;
                registerBtn.innerHTML = show ? 
                    '<i class="fas fa-spinner fa-spin"></i> Creando...' : 
                    '<i class="fas fa-user-plus"></i> Crear Cuenta';
            }
            
            if (googleBtn) {
                googleBtn.disabled = show;
            }
        }

        function showError(message) {
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            alert('Éxito: ' + message);
        }
    </script>
</body>
</html>
