<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitación - ClaroVínculo</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-heart"></i>
                <h1>ClaroVínculo</h1>
            </div>
            <p class="tagline">Facilitando conexiones auténticas y relaciones saludables</p>
        </header>

        <!-- Invitation Page -->
        <div class="invitation-page">
            <div class="invitation-card" id="invitationCard">
                <!-- Loading state -->
                <div id="loadingState" class="loading-state">
                    <i class="fas fa-heart fa-beat" style="font-size: 3rem; color: #ff6b9d; margin-bottom: 1rem;"></i>
                    <p>Cargando invitación...</p>
                </div>

                <!-- Error state -->
                <div id="errorState" class="error-state" style="display: none;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                    <h3>Invitación no encontrada</h3>
                    <p>Lo sentimos, no pudimos encontrar esta invitación. Es posible que el enlace haya expirado o sea incorrecto.</p>
                    <a href="/" class="btn-primary" style="margin-top: 1.5rem; display: inline-block; text-decoration: none;">
                        Crear nueva invitación
                    </a>
                </div>

                <!-- Invitation content -->
                <div id="invitationContent" style="display: none;">
                    <div class="invitation-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    
                    <h3 id="invitationTitle">Te han invitado a un diálogo</h3>
                    
                    <div class="invitation-message" id="invitationMessage">
                        <!-- Mensaje se carga dinámicamente -->
                    </div>
                    
                    <div class="invitation-info">
                        <p><strong>Tipo de relación:</strong> <span id="relationshipType"></span></p>
                        <p><strong>Tiempo estimado:</strong> 5-10 minutos</p>
                    </div>
                    
                    <div class="invitation-actions">
                        <button id="acceptBtn" class="btn-accept">
                            <i class="fas fa-heart"></i>
                            Aceptar invitación
                        </button>
                        <button id="declineBtn" class="btn-decline">
                            <i class="fas fa-times"></i>
                            No, gracias
                        </button>
                    </div>
                </div>

                <!-- Declined state -->
                <div id="declinedState" style="display: none;">
                    <i class="fas fa-heart-broken" style="font-size: 3rem; color: #e53e3e; margin-bottom: 1rem;"></i>
                    <h3>Invitación rechazada</h3>
                    <p>Has decidido no participar en esta invitación. Esto es completamente válido y respetamos tu decisión.</p>
                    
                    <div class="form-group" style="margin-top: 1.5rem;">
                        <label for="declineReason">¿Te gustaría compartir el motivo? (opcional)</label>
                        <textarea id="declineReason" placeholder="Puedes explicar brevemente por qué prefieres no participar..."></textarea>
                    </div>
                    
                    <button id="sendDeclineBtn" class="btn-secondary" style="margin-top: 1rem;">
                        Enviar respuesta
                    </button>
                </div>

                <!-- Accepted state -->
                <div id="acceptedState" style="display: none;">
                    <i class="fas fa-check-circle" style="font-size: 3rem; color: #48bb78; margin-bottom: 1rem;"></i>
                    <h3>¡Gracias por aceptar!</h3>
                    <p>Tu participación significa mucho. Ahora te dirigiremos a un breve cuestionario que nos ayudará a entender mejor tus sentimientos.</p>
                    
                    <div class="next-steps" style="margin: 2rem 0;">
                        <h4>¿Qué sigue?</h4>
                        <ul style="text-align: left; margin: 1rem 0;">
                            <li>Responderás algunas preguntas honestas</li>
                            <li>El proceso toma solo 5-10 minutos</li>
                            <li>Tus respuestas son completamente privadas</li>
                            <li>Recibirás un análisis personalizado</li>
                        </ul>
                    </div>
                    
                    <button id="startQuestionnaireBtn" class="btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        Comenzar cuestionario
                    </button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 ClaroVínculo. Facilitando relaciones auténticas y saludables.</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heart fa-beat"></i>
            <p id="loadingText">Procesando...</p>
        </div>
    </div>

    <script>
        // Inline JavaScript to avoid loading issues
        const API_BASE = '/api';
        let currentInvitation = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializeInvitationPage();
        });

        function initializeInvitationPage() {
            const pathParts = window.location.pathname.split('/');
            const invitationId = pathParts[pathParts.length - 1];

            if (!invitationId) {
                showErrorState('ID de invitación no válido');
                return;
            }

            loadInvitation(invitationId);
            setupEventListeners();
        }

        async function loadInvitation(invitationId) {
            try {
                const response = await fetch(`${API_BASE}/invitations/${invitationId}`);
                const result = await response.json();

                if (result.success) {
                    currentInvitation = result.invitation;
                    displayInvitation(result.invitation);
                } else {
                    throw new Error(result.error || 'Invitación no encontrada');
                }

            } catch (error) {
                console.error('Error loading invitation:', error);
                showErrorState(error.message);
            }
        }

        function displayInvitation(invitation) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('invitationContent').style.display = 'block';

            document.getElementById('invitationTitle').textContent =
                `${invitation.sender_name} te ha invitado a un diálogo`;

            const messageElement = document.getElementById('invitationMessage');
            messageElement.innerHTML = `
                <p>${invitation.message}</p>
                <p style="margin-top: 1rem; font-style: italic; color: #667eea;">
                    "Este proceso toma solo unos minutos y significa mucho para ${invitation.sender_name}.
                    ¿Te gustaría participar?"
                </p>
            `;

            const relationshipTypes = {
                'nueva_conexion': 'Personas que se están conociendo',
                'pareja_establecida': 'Pareja establecida',
                'amistad': 'Amistad',
                'familia': 'Relación familiar'
            };

            document.getElementById('relationshipType').textContent =
                relationshipTypes[invitation.relationship_type] || invitation.relationship_type;

            if (invitation.status !== 'pending') {
                showAlreadyRespondedState(invitation.status);
            }
        }

        function setupEventListeners() {
            document.getElementById('acceptBtn').addEventListener('click', handleAcceptInvitation);
            document.getElementById('declineBtn').addEventListener('click', handleDeclineInvitation);
            document.getElementById('sendDeclineBtn').addEventListener('click', handleSendDecline);
            document.getElementById('startQuestionnaireBtn').addEventListener('click', handleStartQuestionnaire);
        }

        async function handleAcceptInvitation() {
            if (!currentInvitation) return;

            try {
                showLoading(true);

                const response = await fetch(`${API_BASE}/invitations/${currentInvitation.id}/accept`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showAcceptedState();
                    showSuccess('¡Invitación aceptada exitosamente!');
                } else {
                    throw new Error(result.error || 'Error al aceptar la invitación');
                }

            } catch (error) {
                console.error('Error accepting invitation:', error);
                showError(error.message);
            } finally {
                showLoading(false);
            }
        }

        function handleDeclineInvitation() {
            document.getElementById('invitationContent').style.display = 'none';
            document.getElementById('declinedState').style.display = 'block';
        }

        async function handleSendDecline() {
            if (!currentInvitation) return;

            try {
                showLoading(true);

                const reason = document.getElementById('declineReason').value;

                const response = await fetch(`${API_BASE}/invitations/${currentInvitation.id}/decline`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ reason })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('Respuesta enviada. Gracias por tu honestidad.');

                    document.getElementById('declinedState').innerHTML = `
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: #48bb78; margin-bottom: 1rem;"></i>
                        <h3>Respuesta enviada</h3>
                        <p>Gracias por tomarte el tiempo de responder. Tu honestidad es valiosa y respetamos completamente tu decisión.</p>
                        <a href="/" class="btn-secondary" style="margin-top: 1.5rem; display: inline-block; text-decoration: none;">
                            Ir a inicio
                        </a>
                    `;
                } else {
                    throw new Error(result.error || 'Error al enviar la respuesta');
                }

            } catch (error) {
                console.error('Error declining invitation:', error);
                showError(error.message);
            } finally {
                showLoading(false);
            }
        }

        function handleStartQuestionnaire() {
            if (!currentInvitation) return;
            window.location.href = `/questionnaire/${currentInvitation.id}`;
        }

        function showAcceptedState() {
            document.getElementById('invitationContent').style.display = 'none';
            document.getElementById('acceptedState').style.display = 'block';
        }

        function showErrorState(message) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';

            const errorText = document.querySelector('#errorState p');
            if (message && message !== 'Invitación no encontrada') {
                errorText.textContent = message;
            }
        }

        function showAlreadyRespondedState(status) {
            document.getElementById('invitationContent').innerHTML = `
                <i class="fas fa-info-circle" style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;"></i>
                <h3>Invitación ya respondida</h3>
                <p>Esta invitación ya ha sido ${status === 'accepted' ? 'aceptada' : 'rechazada'} anteriormente.</p>
                ${status === 'accepted' ? `
                    <button onclick="window.location.href='/questionnaire/${currentInvitation.id}'" class="btn-primary" style="margin-top: 1.5rem;">
                        <i class="fas fa-arrow-right"></i>
                        Ir al cuestionario
                    </button>
                ` : ''}
                <a href="/" class="btn-secondary" style="margin-top: 1rem; display: inline-block; text-decoration: none;">
                    Crear nueva invitación
                </a>
            `;
        }

        function showLoading(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = show ? 'flex' : 'none';
            }
        }

        function showError(message) {
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            alert('Éxito: ' + message);
        }
    </script>
</body>
</html>
