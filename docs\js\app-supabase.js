// Aplicación principal con Supabase
let currentUser = null;

// Elementos del DOM
const invitationForm = document.getElementById('invitationForm');
const resultSection = document.getElementById('resultSection');
const loadingOverlay = document.getElementById('loadingOverlay');
const createAnotherBtn = document.getElementById('createAnotherBtn');

// Inicialización
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

async function initializeApp() {
    // Verificar estado de autenticación
    await checkAuthState();
    
    // Event listeners
    if (invitationForm) {
        invitationForm.addEventListener('submit', handleCreateInvitation);
    }
    
    if (createAnotherBtn) {
        createAnotherBtn.addEventListener('click', resetForm);
    }

    // Configurar botones de compartir
    setupShareButtons();
    
    // Listener para cambios de autenticación
    SupabaseAuth.onAuthStateChange((event, session) => {
        if (event === 'SIGNED_IN') {
            currentUser = session.user;
            updateUIForAuthenticatedUser();
        } else if (event === 'SIGNED_OUT') {
            currentUser = null;
            updateUIForAnonymousUser();
        }
    });
}

async function checkAuthState() {
    const { success, user } = await SupabaseAuth.getCurrentUser();
    if (success && user) {
        currentUser = user;
        updateUIForAuthenticatedUser();
    } else {
        updateUIForAnonymousUser();
    }
}

function updateUIForAuthenticatedUser() {
    // Mostrar información del usuario
    const userInfo = document.querySelector('.user-info');
    if (userInfo) {
        userInfo.innerHTML = `
            <span>Hola, ${currentUser.email}</span>
            <button onclick="handleSignOut()" class="btn-secondary">Cerrar Sesión</button>
        `;
    }
    
    // Mostrar dashboard link si existe
    const dashboardLink = document.querySelector('.dashboard-link');
    if (dashboardLink) {
        dashboardLink.style.display = 'block';
    }
}

function updateUIForAnonymousUser() {
    // Mostrar botones de login/registro
    const userInfo = document.querySelector('.user-info');
    if (userInfo) {
        userInfo.innerHTML = `
            <a href="login.html" class="btn-secondary">Iniciar Sesión</a>
            <a href="register.html" class="btn-primary">Registrarse</a>
        `;
    }
    
    // Ocultar dashboard link
    const dashboardLink = document.querySelector('.dashboard-link');
    if (dashboardLink) {
        dashboardLink.style.display = 'none';
    }
}

async function handleSignOut() {
    const { success } = await SupabaseAuth.signOut();
    if (success) {
        showSuccess('Sesión cerrada exitosamente');
        window.location.reload();
    }
}

// Crear nueva invitación
async function handleCreateInvitation(e) {
    e.preventDefault();
    
    const formData = new FormData(invitationForm);
    const data = {
        sender_name: formData.get('senderName'),
        recipient_name: formData.get('recipientName'),
        relationship_type: formData.get('relationshipType'),
        message: formData.get('customMessage'),
        sender_id: currentUser?.id || null
    };

    // Validaciones
    if (!data.sender_name || !data.relationship_type) {
        showError('Por favor completa todos los campos requeridos');
        return;
    }

    try {
        showLoading(true);
        
        const result = await SupabaseDB.createInvitation(data);

        if (result.success) {
            // Agregar el enlace completo a la invitación
            result.invitation.link = `${window.location.origin}/invitation.html?id=${result.invitation.id}`;
            showInvitationResult(result.invitation);
        } else {
            throw new Error(result.error || 'Error al crear la invitación');
        }

    } catch (error) {
        console.error('Error:', error);
        showError(error.message || 'Error al crear la invitación');
    } finally {
        showLoading(false);
    }
}

// Mostrar resultado de la invitación
function showInvitationResult(invitation) {
    // Ocultar formulario y mostrar resultado
    document.querySelector('.create-invitation').style.display = 'none';
    resultSection.style.display = 'block';
    
    // Llenar datos del resultado
    const linkInput = document.getElementById('invitationLink');
    linkInput.value = invitation.link;
    
    // Configurar botón de copiar
    const copyBtn = document.getElementById('copyLinkBtn');
    copyBtn.addEventListener('click', () => copyToClipboard(invitation.link));
    
    // Configurar botones de compartir con el link específico
    setupShareButtons(invitation);
    
    // Scroll al resultado
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

// Configurar botones de compartir
function setupShareButtons(invitation = null) {
    const whatsappBtn = document.getElementById('shareWhatsApp');
    const emailBtn = document.getElementById('shareEmail');
    const telegramBtn = document.getElementById('shareTelegram');
    
    if (!invitation) return;
    
    const message = `Hola! ${invitation.sender_name} te ha invitado a participar en un diálogo honesto a través de ClaroVínculo. ${invitation.link}`;
    const encodedMessage = encodeURIComponent(message);
    
    if (whatsappBtn) {
        whatsappBtn.addEventListener('click', () => {
            window.open(`https://wa.me/?text=${encodedMessage}`, '_blank');
        });
    }
    
    if (telegramBtn) {
        telegramBtn.addEventListener('click', () => {
            window.open(`https://t.me/share/url?url=${encodeURIComponent(invitation.link)}&text=${encodeURIComponent(`${invitation.sender_name} te ha invitado a ClaroVínculo`)}`, '_blank');
        });
    }
    
    if (emailBtn) {
        emailBtn.addEventListener('click', () => {
            const subject = encodeURIComponent(`Invitación de ${invitation.sender_name} - ClaroVínculo`);
            const body = encodeURIComponent(`Hola!\n\n${invitation.sender_name} te ha invitado a participar en un diálogo honesto a través de ClaroVínculo.\n\n${invitation.message}\n\nPuedes acceder aquí: ${invitation.link}\n\nSaludos!`);
            window.open(`mailto:?subject=${subject}&body=${body}`);
        });
    }
}

// Copiar al portapapeles
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showSuccess('¡Enlace copiado al portapapeles!');
        
        // Cambiar temporalmente el texto del botón
        const copyBtn = document.getElementById('copyLinkBtn');
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copiado';
        copyBtn.style.background = '#48bb78';
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.style.background = '#667eea';
        }, 2000);
        
    } catch (err) {
        console.error('Error al copiar:', err);
        showError('No se pudo copiar el enlace');
    }
}

// Resetear formulario
function resetForm() {
    invitationForm.reset();
    resultSection.style.display = 'none';
    document.querySelector('.create-invitation').style.display = 'block';
    
    // Scroll al formulario
    document.querySelector('.create-invitation').scrollIntoView({ behavior: 'smooth' });
}

// Mostrar/ocultar loading
function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
    
    // Deshabilitar botón de envío
    const submitBtn = document.getElementById('createBtn');
    if (submitBtn) {
        submitBtn.disabled = show;
        submitBtn.innerHTML = show ? 
            '<i class="fas fa-spinner fa-spin"></i> Creando...' : 
            '<i class="fas fa-paper-plane"></i> Crear Invitación';
    }
}

// Mostrar mensajes de error
function showError(message) {
    showNotification(message, 'error');
}

// Mostrar mensajes de éxito
function showSuccess(message) {
    showNotification(message, 'success');
}

// Sistema de notificaciones (igual que antes)
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#fed7d7' : type === 'success' ? '#c6f6d5' : '#bee3f8'};
        color: ${type === 'error' ? '#c53030' : type === 'success' ? '#2f855a' : '#2b6cb0'};
        padding: 1rem 1.5rem;
        border-radius: 12px;
        border: 1px solid ${type === 'error' ? '#feb2b2' : type === 'success' ? '#9ae6b4' : '#90cdf4'};
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 1001;
        display: flex;
        align-items: center;
        gap: 1rem;
        max-width: 400px;
        animation: slideIn 0.3s ease;
    `;
    
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }
        .notification-close {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .notification-close:hover {
            opacity: 1;
        }
    `;
    document.head.appendChild(style);
    
    document.body.appendChild(notification);
    
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        notification.remove();
    });
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Utilidades para validación
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validateForm(data) {
    const errors = [];
    
    if (!data.sender_name || data.sender_name.trim().length < 2) {
        errors.push('El nombre debe tener al menos 2 caracteres');
    }
    
    if (!data.relationship_type) {
        errors.push('Debes seleccionar un tipo de relación');
    }
    
    return errors;
}
