A continuación, te presento un **prompt detallado y optimizado** para instruir a una inteligencia artificial en la creación de una aplicación web que fomente relaciones sanas entre parejas, familiares, amigos y personas que se están conociendo. Este prompt está diseñado desde una perspectiva multidisciplinaria que combina psicología, sociología, relaciones humanas, dinámicas de pareja, desarrollo personal, ingeniería de software y estrategias de marketing digital, atendiendo a tu solicitud de estructurar la idea y desarrollarla de manera clara y funcional.

---

### **Prompt para la Creación de la Aplicación Web "Relaciones Sanas"**

#### **Introducción**
Eres una inteligencia artificial con experiencia avanzada en psicología, sociología, relaciones humanas, dinámicas de pareja, desarrollo personal, ingeniería de software y marketing digital. Tu misión es diseñar y desarrollar una aplicación web innovadora llamada **"Relaciones Sanas"**, una plataforma que promueve conexiones saludables y comunicación abierta entre parejas (en cualquier etapa), familiares, amigos y personas que recién se están conociendo. La aplicación debe permitir a los usuarios evaluar el interés mutuo, la disponibilidad emocional, la compatibilidad y el estado de sus relaciones, ofreciendo herramientas prácticas y análisis empáticos basados en inteligencia artificial para fortalecer vínculos o tomar decisiones informadas.

#### **Objetivo de la Aplicación**
"Relaciones Sanas" busca ser un espacio seguro, intuitivo y accesible donde los usuarios puedan:
- Crear perfiles personales de forma rápida y sencilla.
- Iniciar diálogos o evaluaciones con otras personas mediante invitaciones personalizadas.
- Responder encuestas directas y específicas adaptadas al tipo de relación.
- Recibir análisis detallados y retroalimentación generada por IA para comprender mejor sus relaciones.
- Acceder a sugerencias y recursos que promuevan su bienestar emocional y el desarrollo de relaciones saludables.

#### **Funcionalidades Principales**

1. **Creación de Perfiles**
   - Los usuarios podrán crear perfiles básicos con información como nombre, foto (opcional) y una breve descripción.
   - Para facilitar el registro, se integrará la opción de iniciar sesión con cuentas de Google u otras redes sociales, minimizando el tiempo de configuración inicial.
   - Si el usuario recibe una invitación y no tiene perfil, podrá crearlo rápidamente después de aceptar o posponerlo para más adelante.

2. **Envío y Recepción de Invitaciones**
   - **Proceso**: Un usuario (remitente) selecciona a otra persona (destinatario) y define el tipo de relación (pareja, amigo, familiar, conocido) y lo que desea evaluar (interés, disponibilidad emocional, estado de la relación, etc.).
   - **Invitación**: El destinatario recibe un enlace con un mensaje personalizado, por ejemplo:  
     _"Hola, [Nombre del Destinatario], [Nombre del Remitente] te ha invitado a dialogar para conocerse mejor. ¿Te gustaría aceptarla? Solo tomará unos minutos y significa mucho para [Remitente]."_  
   - **Evaluación Inicial**: La aplicación registra:
     - El tiempo que tarda el destinatario en responder.
     - Si acepta, rechaza o pospone la invitación.
     - Una explicación opcional si decide rechazar (e.g., "No estoy listo ahora", "Prefiero hablar en persona").
   - El remitente recibe una notificación con el resultado inicial de esta interacción.

3. **Encuestas Personalizadas**
   - **Tipos de Relación**: Las encuestas se adaptan según el tipo de relación seleccionado (pareja estable, noviazgo, amigos, familiares, personas conociéndose).
   - **Diseño de las Preguntas**:  
     - Directas, específicas y encadenadas (las respuestas previas determinan las siguientes preguntas).  
     - Ejemplo para personas que se están conociendo:  
       - "¿Te atrae físicamente [Nombre]? (Escala del 1 al 10)"  
       - "¿Cómo describirías tu disponibilidad emocional actual? (e.g., abierto, reservado, no disponible)"  
       - "¿Te ves en una relación de noviazgo con [Nombre] en el futuro? (Sí/No/Tal vez)"  
       - "¿Estás en una relación actualmente? (Sí/No)"  
     - Ejemplo para parejas establecidas:  
       - "¿Cómo calificarías la comunicación con [Nombre]? (1-10)"  
       - "¿Sientes que hay algo que podrían mejorar juntos?"  
   - **Formato Flexible**: Los usuarios pueden responder con texto, selección múltiple, escalas numéricas o incluso mensajes de voz/video para personalizar la experiencia.
   - **Introducción Opcional**: Antes de la encuesta, el remitente puede incluir un breve saludo en video, voz o texto explicando su intención (e.g., "Hola, quería saber cómo te sientes respecto a nosotros").

4. **Análisis y Retroalimentación con IA**
   - La IA procesa las respuestas y genera un resumen claro, empático y organizado para ambas partes.
   - **Ejemplo de Retroalimentación**:  
     _"En base a tus respuestas, hemos analizado lo siguiente: Parece que en este momento no te encuentras disponible para una relación sentimental seria. Como mencionaste, acabas de salir de una relación y necesitas tiempo para sanar. Reconocer esto es importante para evitar malos momentos para ti y para [Nombre]. ¿Te gustaría enviarle esta respuesta a [Nombre]?"_  
   - Incluye sugerencias prácticas, como:  
     - Para parejas: "Consideren hablar más sobre sus expectativas a futuro".  
     - Para amigos: "Podrías proponerle una actividad para reconectar".  
   - Permite al usuario aprobar o editar la respuesta antes de enviarla.

5. **Recursos para el Bienestar Emocional**
   - Ofrece enlaces a contenido educativo (artículos, videos, podcasts) sobre comunicación, manejo de emociones y desarrollo personal.
   - En casos de respuestas que indiquen conflicto o necesidad de apoyo (e.g., tristeza profunda, ruptura reciente), sugiere recursos profesionales como líneas de ayuda.

6. **Privacidad y Seguridad**
   - Todos los datos estarán protegidos con medidas de seguridad avanzadas.
   - Los usuarios deben dar consentimiento explícito para compartir respuestas o información personal con otros.

7. **Interfaz de Usuario**
   - Diseña una interfaz limpia, intuitiva y responsive, compatible con móviles y computadoras.
   - Incluye botones claros como "Enviar Invitación", "Responder Ahora" o "Ver Análisis".

8. **Escalabilidad y Personalización**
   - Permite añadir nuevos tipos de relaciones o encuestas con el tiempo.
   - Los usuarios avanzados pueden personalizar preguntas o crear encuestas propias.

#### **Consideraciones Éticas y Culturales**
- Asegúrate de que las preguntas y análisis sean inclusivos, respetando la diversidad cultural y las diferentes formas de entender las relaciones.
- Promueve siempre la honestidad, el respeto mutuo y la salud emocional, dejando claro que la aplicación es una herramienta de apoyo, no un reemplazo de la interacción humana.

#### **Nombre de la Aplicación**
Entre las opciones que propusiste ("Healthy Heart", "Amor y Paz", "Corazón Sano"), el nombre **"Relaciones Sanas"** destaca por su claridad, universalidad y alineación con la misión de la aplicación. Es directo, inclusivo y refleja el enfoque en todo tipo de vínculos saludables.

#### **Instrucciones Finales**
Desarrolla "Relaciones Sanas" como una plataforma transformadora que no solo evalúe relaciones, sino que también eduque y empodere a los usuarios para construir conexiones más auténticas y significativas. Impregna cada funcionalidad con empatía, respeto y un enfoque práctico hacia las emociones humanas. ¡Comienza a crear esta herramienta revolucionaria para las relaciones del siglo XXI!

---

Este prompt encapsula tu visión, estructurándola en un diseño funcional y optimizado, con un enfoque claro en la experiencia del usuario y el impacto emocional positivo. ¿Qué te parece? ¿Hay algo más que quieras ajustar o añadir?