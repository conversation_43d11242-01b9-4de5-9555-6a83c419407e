// Configuración de Supabase
const SUPABASE_CONFIG = {
  url: 'https://uxetumzbfssctqthamvu.supabase.co', // Reemplaza con tu URL
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV4ZXR1bXpiZnNzY3RxdGhhbXZ1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4ODYyODYsImV4cCI6MjA2NDQ2MjI4Nn0.7mj8nRL_m1AaaIaurLYgy4ji-amdPS3v0UjstYsp5Vc' // Reemplaza con tu anon key
};

// Importar Supabase desde CDN
const { createClient } = supabase;

// Crear cliente de Supabase
const supabaseClient = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);

// Utilidades de autenticación
class SupabaseAuth {
  static async signUp(email, password, userData = {}) {
    try {
      const { data, error } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      });
      
      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async signIn(email, password) {
    try {
      const { data, error } = await supabaseClient.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async signInWithGoogle() {
    try {
      const { data, error } = await supabaseClient.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/healthy-love_augment_proove/`
        }
      });
      
      if (error) throw error;
      return { success: true, data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async signOut() {
    try {
      const { error } = await supabaseClient.auth.signOut();
      if (error) throw error;
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabaseClient.auth.getUser();
      if (error) throw error;
      return { success: true, user };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static onAuthStateChange(callback) {
    return supabaseClient.auth.onAuthStateChange(callback);
  }
}

// Utilidades de base de datos
class SupabaseDB {
  // Invitaciones
  static async createInvitation(invitationData) {
    try {
      const { data, error } = await supabaseClient
        .from('invitations')
        .insert([invitationData])
        .select()
        .single();
      
      if (error) throw error;
      return { success: true, invitation: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getInvitation(id) {
    try {
      const { data, error } = await supabaseClient
        .from('invitations')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return { success: true, invitation: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async updateInvitationStatus(id, status, declineReason = null) {
    try {
      const updateData = { 
        status, 
        updated_at: new Date().toISOString() 
      };
      
      if (declineReason) {
        updateData.decline_reason = declineReason;
      }

      const { data, error } = await supabaseClient
        .from('invitations')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return { success: true, invitation: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getUserInvitations(userId) {
    try {
      const { data, error } = await supabaseClient
        .from('invitations')
        .select('*')
        .eq('sender_id', userId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return { success: true, invitations: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Respuestas del cuestionario
  static async saveQuestionnaireResponse(responseData) {
    try {
      const { data, error } = await supabaseClient
        .from('questionnaire_responses')
        .insert([responseData])
        .select()
        .single();
      
      if (error) throw error;
      return { success: true, response: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getQuestionnaireResponse(invitationId) {
    try {
      const { data, error } = await supabaseClient
        .from('questionnaire_responses')
        .select('*')
        .eq('invitation_id', invitationId)
        .single();
      
      if (error) throw error;
      return { success: true, response: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Análisis
  static async saveAnalysis(analysisData) {
    try {
      const { data, error } = await supabaseClient
        .from('analyses')
        .insert([analysisData])
        .select()
        .single();
      
      if (error) throw error;
      return { success: true, analysis: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async getAnalysis(invitationId) {
    try {
      const { data, error } = await supabaseClient
        .from('analyses')
        .select('*')
        .eq('invitation_id', invitationId)
        .single();
      
      if (error) throw error;
      return { success: true, analysis: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  // Perfiles
  static async getProfile(userId) {
    try {
      const { data, error } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) throw error;
      return { success: true, profile: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  static async updateProfile(userId, profileData) {
    try {
      const { data, error } = await supabaseClient
        .from('profiles')
        .update({ ...profileData, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single();
      
      if (error) throw error;
      return { success: true, profile: data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// Exportar para uso global
window.SupabaseAuth = SupabaseAuth;
window.SupabaseDB = SupabaseDB;
window.supabaseClient = supabaseClient;
