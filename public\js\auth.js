// Authentication functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeAuthPage();
});

function initializeAuthPage() {
    const currentPage = window.location.pathname;

    if (currentPage === '/login') {
        initializeLoginPage();
    } else if (currentPage === '/register') {
        initializeRegisterPage();
    }

    // Setup password toggles
    setupPasswordToggles();

    // Check for URL parameters (like error messages)
    checkUrlParams();

    // Check if Google OAuth is available
    checkGoogleOAuthAvailability();
}

function initializeLoginPage() {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
}

function initializeRegisterPage() {
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Setup password strength indicator
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', updatePasswordStrength);
    }
    
    // Setup password confirmation validation
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordConfirmation);
    }
}

async function handleLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        email: formData.get('email'),
        password: formData.get('password')
    };
    
    // Basic validation
    if (!data.email || !data.password) {
        ClaroVinculo.showError('Por favor completa todos los campos');
        return;
    }
    
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Iniciando sesión...';
        
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            ClaroVinculo.showSuccess('¡Sesión iniciada exitosamente!');
            
            // Redirect to dashboard after a short delay
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        } else {
            throw new Error(result.error || 'Error al iniciar sesión');
        }
        
    } catch (error) {
        console.error('Error en login:', error);
        ClaroVinculo.showError(error.message || 'Error al iniciar sesión');
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

async function handleRegister(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword'),
        acceptTerms: formData.get('acceptTerms'),
        newsletter: formData.get('newsletter')
    };
    
    // Validation
    const validationErrors = validateRegistrationData(data);
    if (validationErrors.length > 0) {
        ClaroVinculo.showError(validationErrors.join(', '));
        return;
    }
    
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Creando cuenta...';
        
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                name: data.name,
                email: data.email,
                password: data.password
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            ClaroVinculo.showSuccess('¡Cuenta creada exitosamente!');
            
            // Redirect to dashboard after a short delay
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        } else {
            throw new Error(result.error || 'Error al crear la cuenta');
        }
        
    } catch (error) {
        console.error('Error en registro:', error);
        ClaroVinculo.showError(error.message || 'Error al crear la cuenta');
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

function validateRegistrationData(data) {
    const errors = [];
    
    if (!data.name || data.name.trim().length < 2) {
        errors.push('El nombre debe tener al menos 2 caracteres');
    }
    
    if (!data.email || !isValidEmail(data.email)) {
        errors.push('Email inválido');
    }
    
    if (!data.password || data.password.length < 6) {
        errors.push('La contraseña debe tener al menos 6 caracteres');
    }
    
    if (data.password !== data.confirmPassword) {
        errors.push('Las contraseñas no coinciden');
    }
    
    if (!data.acceptTerms) {
        errors.push('Debes aceptar los términos y condiciones');
    }
    
    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function setupPasswordToggles() {
    const toggles = document.querySelectorAll('.password-toggle');
    
    toggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        });
    });
}

function updatePasswordStrength() {
    const password = document.getElementById('password').value;
    const strengthFill = document.getElementById('strengthFill');
    const strengthText = document.getElementById('strengthText');
    
    if (!password) {
        strengthFill.style.width = '0%';
        strengthFill.style.background = '#e2e8f0';
        strengthText.textContent = 'Ingresa una contraseña';
        return;
    }
    
    let strength = 0;
    let strengthLabel = '';
    let strengthColor = '';
    
    // Length check
    if (password.length >= 6) strength += 1;
    if (password.length >= 8) strength += 1;
    
    // Character variety checks
    if (/[a-z]/.test(password)) strength += 1;
    if (/[A-Z]/.test(password)) strength += 1;
    if (/[0-9]/.test(password)) strength += 1;
    if (/[^A-Za-z0-9]/.test(password)) strength += 1;
    
    // Determine strength level
    if (strength <= 2) {
        strengthLabel = 'Débil';
        strengthColor = '#e53e3e';
    } else if (strength <= 4) {
        strengthLabel = 'Media';
        strengthColor = '#ed8936';
    } else {
        strengthLabel = 'Fuerte';
        strengthColor = '#48bb78';
    }
    
    const percentage = Math.min((strength / 6) * 100, 100);
    
    strengthFill.style.width = `${percentage}%`;
    strengthFill.style.background = strengthColor;
    strengthText.textContent = strengthLabel;
    strengthText.style.color = strengthColor;
}

function validatePasswordConfirmation() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmInput = document.getElementById('confirmPassword');
    
    if (confirmPassword && password !== confirmPassword) {
        confirmInput.style.borderColor = '#e53e3e';
        confirmInput.style.boxShadow = '0 0 0 3px rgba(229, 62, 62, 0.1)';
    } else {
        confirmInput.style.borderColor = '#e2e8f0';
        confirmInput.style.boxShadow = 'none';
    }
}

function checkUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    
    if (error) {
        let errorMessage = 'Error de autenticación';
        
        switch (error) {
            case 'google_auth_failed':
                errorMessage = 'Error al autenticar con Google. Inténtalo de nuevo.';
                break;
            case 'google_not_configured':
                errorMessage = 'Login con Google no está configurado. Usa email y contraseña para iniciar sesión.';
                break;
            case 'session_expired':
                errorMessage = 'Tu sesión ha expirado. Por favor inicia sesión nuevamente.';
                break;
            case 'unauthorized':
                errorMessage = 'No tienes autorización para acceder a esa página.';
                break;
        }
        
        ClaroVinculo.showError(errorMessage);
        
        // Clean URL
        window.history.replaceState({}, document.title, window.location.pathname);
    }
}

async function checkGoogleOAuthAvailability() {
    try {
        // Test if Google OAuth is available by making a request to the Google auth endpoint
        const response = await fetch('/api/auth/google', {
            method: 'HEAD',
            redirect: 'manual'
        });

        // If we get redirected to login with an error, Google OAuth is not configured
        if (response.status === 0 || response.url.includes('error=google_not_configured')) {
            hideGoogleAuthButtons();
        }
    } catch (error) {
        // If there's an error, assume Google OAuth is not configured
        hideGoogleAuthButtons();
    }
}

function hideGoogleAuthButtons() {
    const googleAuthSections = document.querySelectorAll('.google-auth');
    const authDividers = document.querySelectorAll('.auth-divider');

    googleAuthSections.forEach(section => {
        section.style.display = 'none';
    });

    authDividers.forEach(divider => {
        divider.style.display = 'none';
    });

    console.log('ℹ️ Google OAuth no está configurado. Solo se mostrará login con email/contraseña.');
}

// Export functions for use in other scripts
window.AuthModule = {
    handleLogin,
    handleRegister,
    validateRegistrationData,
    isValidEmail,
    checkGoogleOAuthAvailability
};
