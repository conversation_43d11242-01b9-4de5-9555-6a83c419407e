# Configuración de Supabase para ClaroVínculo

## 1. <PERSON>rear Proyecto en Supabase

1. Ve a [supabase.com](https://supabase.com)
2. Crea una cuenta y un nuevo proyecto
3. Anota la URL y la API Key (anon key)

## 2. Configuración de Base de Datos

### Tablas a crear en Supabase SQL Editor:

```sql
-- Tabla de usuarios (Supabase Auth se encarga de esto automáticamente)
-- Solo necesitamos una tabla de perfiles adicional

-- Tabla de perfiles de usuario
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de invitaciones
CREATE TABLE invitations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  sender_id UUID REFERENCES auth.users(id),
  sender_name TEXT NOT NULL,
  recipient_name TEXT,
  recipient_email TEXT,
  relationship_type TEXT NOT NULL,
  message TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
  decline_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')
);

-- Tabla de respuestas del cuestionario
CREATE TABLE questionnaire_responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  invitation_id UUID REFERENCES invitations(id) ON DELETE CASCADE,
  respondent_name TEXT,
  responses JSONB NOT NULL,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabla de análisis/resultados
CREATE TABLE analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  invitation_id UUID REFERENCES invitations(id) ON DELETE CASCADE,
  sender_analysis JSONB,
  recipient_analysis JSONB,
  compatibility_score INTEGER,
  recommendations TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Índices para mejor performance
CREATE INDEX idx_invitations_sender_id ON invitations(sender_id);
CREATE INDEX idx_invitations_status ON invitations(status);
CREATE INDEX idx_questionnaire_responses_invitation_id ON questionnaire_responses(invitation_id);
CREATE INDEX idx_analyses_invitation_id ON analyses(invitation_id);

-- RLS (Row Level Security) Policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE questionnaire_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE analyses ENABLE ROW LEVEL SECURITY;

-- Políticas de seguridad
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own invitations" ON invitations
  FOR SELECT USING (auth.uid() = sender_id);

CREATE POLICY "Users can create invitations" ON invitations
  FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Anyone can view pending invitations" ON invitations
  FOR SELECT USING (status = 'pending');

CREATE POLICY "Anyone can update invitation status" ON invitations
  FOR UPDATE USING (true);

-- Función para crear perfil automáticamente
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger para crear perfil cuando se registra un usuario
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
```

## 3. Configuración de Autenticación

En el dashboard de Supabase:

1. Ve a Authentication > Settings
2. Configura Google OAuth:
   - Habilita Google provider
   - Agrega Client ID y Client Secret de Google
3. Configura Email Auth:
   - Habilita Email provider
   - Configura templates de email si quieres

## 4. Variables de Entorno

Crea un archivo `config.js` en tu frontend:

```javascript
const SUPABASE_CONFIG = {
  url: 'TU_SUPABASE_URL',
  anonKey: 'TU_SUPABASE_ANON_KEY'
};
```

## 5. Configuración de CORS

En Supabase Dashboard > Settings > API:
- Agrega tu dominio de GitHub Pages a CORS origins
- Ejemplo: `https://tu-usuario.github.io`
