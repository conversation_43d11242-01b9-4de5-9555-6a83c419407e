[build]
  # Directorio donde están los archivos de la aplicación
  publish = "docs"
  
  # Comando de build (no necesario para archivos estáticos)
  command = "echo 'No build needed for static files'"

[build.environment]
  # Variables de entorno si las necesitas
  NODE_VERSION = "18"

# Configuración para SPAs (Single Page Applications)
[[redirects]]
  from = "/docs/*"
  to = "/docs/:splat"
  status = 200

# Redirecciones para rutas limpias
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Headers de seguridad
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Headers para archivos estáticos
[[headers]]
  for = "/js/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/styles/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
