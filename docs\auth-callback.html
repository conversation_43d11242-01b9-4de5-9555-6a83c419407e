<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Autenticando... - ClaroVínculo</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        .auth-callback {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 2rem;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4285f4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success-icon {
            font-size: 3rem;
            color: #48bb78;
            margin-bottom: 1rem;
        }
        
        .error-icon {
            font-size: 3rem;
            color: #e53e3e;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="auth-callback">
        <div id="loading" class="loading-state">
            <div class="spinner"></div>
            <h2>Autenticando con Google...</h2>
            <p>Por favor espera mientras procesamos tu autenticación.</p>
        </div>
        
        <div id="success" class="success-state" style="display: none;">
            <div class="success-icon">✅</div>
            <h2>¡Autenticación exitosa!</h2>
            <p>Redirigiendo a la aplicación...</p>
        </div>
        
        <div id="error" class="error-state" style="display: none;">
            <div class="error-icon">❌</div>
            <h2>Error de autenticación</h2>
            <p id="error-message">Hubo un problema con la autenticación.</p>
            <button onclick="window.location.href='login.html'" class="btn-primary" style="margin-top: 1rem;">
                Intentar de nuevo
            </button>
        </div>
    </div>

    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/supabase-client.js"></script>

    <script>
        async function handleAuthCallback() {
            try {
                // Verificar si hay un hash con tokens en la URL
                const hashParams = new URLSearchParams(window.location.hash.substring(1));
                const accessToken = hashParams.get('access_token');
                const refreshToken = hashParams.get('refresh_token');
                
                if (accessToken) {
                    // Hay tokens en la URL, procesarlos
                    console.log('Tokens encontrados en URL, procesando...');
                    
                    // Esperar un momento para que Supabase procese la sesión
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    // Verificar el estado de autenticación
                    const { success, user } = await SupabaseAuth.getCurrentUser();
                    
                    if (success && user) {
                        // Autenticación exitosa
                        showSuccess();
                        
                        // Limpiar la URL de tokens por seguridad
                        window.history.replaceState({}, document.title, window.location.pathname);
                        
                        // Redirigir después de un momento
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 2000);
                    } else {
                        throw new Error('No se pudo verificar la autenticación');
                    }
                } else {
                    // No hay tokens, verificar si ya está autenticado
                    const { success, user } = await SupabaseAuth.getCurrentUser();
                    
                    if (success && user) {
                        // Ya está autenticado
                        showSuccess();
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    } else {
                        // No está autenticado y no hay tokens
                        throw new Error('No se encontraron credenciales de autenticación');
                    }
                }
                
            } catch (error) {
                console.error('Error en callback de autenticación:', error);
                showError(error.message);
            }
        }
        
        function showSuccess() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('success').style.display = 'block';
            document.getElementById('error').style.display = 'none';
        }
        
        function showError(message) {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('success').style.display = 'none';
            document.getElementById('error').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }
        
        // Ejecutar cuando la página carga
        document.addEventListener('DOMContentLoaded', handleAuthCallback);
    </script>
</body>
</html>
