# Prompt Detallado para Desarrollo de Aplicación "ClaroVínculo"

## Contexto y Propósito Fundamental

Eres una IA especializada en crear una aplicación web revolucionaria que facilita la comunicación auténtica y el establecimiento de relaciones saludables. La plataforma debe combinar principios de psicología relacional, comunicación no violenta, y análisis de compatibilidad emocional para crear un espacio seguro donde las personas puedan expresar y conocer intenciones reales sin el miedo al rechazo o malentendidos.

## Arquitectura Conceptual de la Aplicación

### Nombre Sugerido: "ClaroVínculo"
*Alternativas: "Sincero", "PuenteCora", "VínculoReal", "AbreCora"*

### Tipos de Relación Soportados:
1. **Nuevas Conexiones** (personas que se están conociendo)
2. **Relaciones Románticas** (parejas establecidas)
3. **Vínculos Familiares** (padres-hijos, hermanos, etc.)
4. **Amistades** (nuevas y establecidas)
5. **Relaciones de Transición** (ex-parejas, reconciliaciones)

## Flujo de Usuario Detallado

### Fase 1: Iniciación de Diálogo
```
REMITENTE crea perfil → Selecciona tipo de relación → Configura intención
→ Sistema genera link personalizado → Envía invitación
```

**Mensaje de Invitación Personalizado:**
"Hola [Nombre], [RemitenteName] te ha invitado a participar en un diálogo honesto y respetuoso para conocerse mejor/aclarar sentimientos/fortalecer su relación. Este proceso toma solo 5-10 minutos y significa mucho para [él/ella]. ¿Te gustaría participar?"

### Fase 2: Evaluación de Respuesta
El sistema analiza automáticamente:
- **Tiempo de respuesta** (inmediato, horas, días)
- **Tipo de respuesta** (acepta, rechaza, pospone)
- **Tono de respuesta** (entusiasta, dudoso, cortés, evasivo)

### Fase 3: Cuestionario Adaptativo Inteligente

#### Para Nuevas Conexiones:
```
Bloque 1: Atracción y Interés Inicial
- "En una escala del 1-10, ¿qué nivel de interés sientes hacia [Nombre]?"
- "¿Qué tipo de conexión percibes? (Amistad, Romántica, Profesional, Familiar)"
- "¿Cómo describirías tu estado emocional al interactuar con [Nombre]?"

Bloque 2: Disponibilidad Emocional
- "¿Te encuentras emocionalmente disponible para una nueva relación?"
- "¿Hay alguna situación personal que podría afectar tu capacidad de conectar?"
- "En este momento de tu vida, ¿qué tipo de relación buscas?"

Bloque 3: Proyección a Futuro
- "¿Te ves desarrollando una relación más profunda con [Nombre]?"
- "¿Qué expectativas tienes sobre esta posible conexión?"
- "¿Hay algo que te gustaría que [Nombre] supiera sobre ti?"
```

#### Para Parejas Establecidas:
```
Bloque 1: Satisfacción Relacional
- "¿Cómo calificarías tu nivel de felicidad actual en la relación? (1-10)"
- "¿Sientes que tus necesidades emocionales están siendo satisfechas?"
- "¿Qué aspectos de tu relación te generan más alegría?"

Bloque 2: Comunicación y Conflictos
- "¿Con qué frecuencia sientes que puedes expresarte libremente?"
- "¿Cómo manejan los desacuerdos como pareja?"
- "¿Hay temas importantes que evitan discutir?"

Bloque 3: Visión de Futuro
- "¿Cómo imaginas su relación en el próximo año?"
- "¿Qué cambios te gustaría ver en su dinámica?"
- "¿Te sientes comprometido/a con el crecimiento de esta relación?"
```

## Sistema de Análisis IA

### Procesamiento de Respuestas:
La IA debe analizar:
1. **Patrones de respuesta** (coherencia, contradicciones)
2. **Indicadores emocionales** (entusiasmo, dudas, resistencias)
3. **Compatibilidad relacional** (expectativas alineadas/desalineadas)
4. **Niveles de madurez emocional** (autoconsciencia, disponibilidad)

### Generación de Insights:
```
Ejemplo de Análisis para Nuevas Conexiones:
"Basado en tus respuestas, hemos identificado que:
- Muestras un interés genuino hacia [Nombre] (nivel 7/10)
- Tu disponibilidad emocional es limitada debido a experiencias recientes
- Prefieres una conexión gradual antes que compromiso inmediato
- Valoras la honestidad y comunicación abierta

RECOMENDACIÓN: Te sugerimos comunicar a [Nombre] tu interés, pero también tu necesidad de tiempo para desarrollar la conexión de manera saludable."
```

## Características Técnicas Avanzadas

### 1. Sistema de Retroalimentación Adaptativa
- Las preguntas se ajustan según respuestas previas
- Algoritmo de ramificación inteligente
- Detección de patrones de evasión o deshonestidad

### 2. Interfaz de Usuario Empática
- Diseño calmante con colores suaves
- Indicadores de progreso no invasivos
- Mensajes de validación y apoyo
- Opción de pausar y continuar más tarde

### 3. Privacidad y Seguridad
- Cifrado end-to-end de todas las comunicaciones
- Opción de anonimato parcial
- Control granular sobre qué información compartir
- Posibilidad de eliminar datos completamente

### 4. Sistema de Comunicación Post-Análisis
- Generación automática de respuestas sugeridas
- Opciones de personalización del mensaje
- Calendario integrado para planificar conversaciones
- Recursos educativos sobre comunicación saludable

## Monetización Ética

### Modelo Freemium:
- **Versión Gratuita**: 3 análisis por mes, funciones básicas
- **Premium**: Análisis ilimitados, insights avanzados, herramientas de seguimiento
- **Terapia Integrada**: Conexión con profesionales certificados

### Funciones Premium:
- Seguimiento longitudinal de relaciones
- Análisis comparativo entre múltiples conexiones
- Recomendaciones personalizadas de crecimiento
- Acceso a biblioteca de recursos educativos

## Consideraciones Psicológicas Críticas

### Principios de Diseño:
1. **No Juicio**: La plataforma nunca debe hacer que alguien se sienta "mal" por sus respuestas
2. **Empoderamiento**: Cada usuario debe sentir control sobre su proceso
3. **Validación**: Reconocer que todas las emociones y estados son válidos
4. **Crecimiento**: Enfocar en el desarrollo personal, no en el "éxito" relacional

### Salvaguardas Emocionales:
- Detección de señales de alarma (toxicidad, manipulación)
- Recursos de crisis para situaciones de riesgo
- Límites en la frecuencia de uso para evitar obsesión
- Conexión con servicios de salud mental cuando sea necesario

## Casos de Uso Específicos

### Escenario 1: "Ana conoce a Luis"
Ana siente atracción hacia Luis pero no sabe si es recíproca. Crea una invitación tipo "nueva conexión". Luis responde mostrando interés moderado pero revela que acaba de terminar una relación. El sistema sugiere a Ana darle espacio mientras mantiene una amistad, y a Luis explorar sus patrones relacionales.

### Escenario 2: "Pareja en Crisis"
Carmen y Pedro llevan 5 años juntos pero sienten distancia. Ambos completan evaluaciones que revelan necesidades no comunicadas. El sistema identifica puntos de desconexión y sugiere conversaciones específicas, proporcionando guías de comunicación.

### Escenario 3: "Reconciliación Familiar"
Un padre quiere reconstruir relación con hijo adulto después de años de distancia. La plataforma facilita un diálogo estructurado que permite expresar arrepentimiento y establecer nuevos límites saludables.

## Métricas de Éxito

### Indicadores Cuantitativos:
- Tasa de finalización de evaluaciones
- Satisfacción post-proceso (NPS)
- Frecuencia de uso continuado
- Conversiones a premium

### Indicadores Cualitativos:
- Testimonios de mejora relacional
- Reducción de conflictos reportados
- Incremento en autoconsciencia emocional
- Historias de conexiones auténticas logradas

## Implementación Técnica Sugerida

### Stack Tecnológico:
- **Frontend**: React/Vue.js con diseño responsive
- **Backend**: Node.js/Python con APIs RESTful
- **Base de Datos**: PostgreSQL con encriptación
- **IA/ML**: TensorFlow/PyTorch para análisis de patrones
- **Comunicación**: WebSockets para chat en tiempo real

### Fases de Desarrollo:
1. **MVP**: Funcionalidad básica para nuevas conexiones
2. **Expansión**: Todos los tipos de relación
3. **Inteligencia**: IA avanzada y personalización
4. **Ecosistema**: Integración con terapeutas y recursos

## Consideraciones Legales y Éticas

### Compliance:
- GDPR/LOPD para protección de datos
- Términos claros sobre uso de información
- Política de menores de edad
- Certificaciones de seguridad

### Responsabilidad Social:
- Colaboración con organizaciones de salud mental
- Investigación académica sobre efectividad
- Accesibilidad para personas con discapacidades
- Versiones en múltiples idiomas

---

**Instrucciones Finales para el Desarrollo:**

Crea una aplicación que sea más que una herramienta de comunicación; que sea un catalalizador para relaciones más honestas, saludables y satisfactorias. Prioriza siempre el bienestar emocional de los usuarios sobre métricas de engagement. La tecnología debe servir para acercar a las personas de manera auténtica, no para crear dependencia o ansiedad.

El éxito se medirá no solo en usuarios activos, sino en vidas mejoradas, relaciones sanadas, y conexiones genuinas facilitadas.