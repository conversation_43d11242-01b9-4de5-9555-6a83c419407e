<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Se<PERSON><PERSON> - <PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-heart"></i>
                <h1><PERSON>lar<PERSON><PERSON><PERSON><PERSON><PERSON></h1>
            </div>
            <p class="tagline">Facilitando conexiones auténticas y relaciones saludables</p>
        </header>

        <!-- Login Form -->
        <div class="auth-container">
            <div class="auth-card">
                <h2>Iniciar Sesión</h2>
                <p class="auth-subtitle">Accede a tu cuenta para gestionar tus invitaciones</p>

                <form id="loginForm" class="auth-form">
                    <div class="form-group">
                        <label for="email">Correo Electrónico</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Contraseña</label>
                        <input type="password" id="password" name="password" required>
                    </div>

                    <button type="submit" id="loginBtn" class="btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        Iniciar Sesión
                    </button>
                </form>

                <div class="auth-divider">
                    <span>o</span>
                </div>

                <button id="googleLoginBtn" class="btn-google">
                    <i class="fab fa-google"></i>
                    Continuar con Google
                </button>

                <div class="auth-links">
                    <p>¿No tienes cuenta? <a href="register.html">Regístrate aquí</a></p>
                    <p><a href="index.html">Volver al inicio</a></p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 ClaroVínculo. Facilitando relaciones auténticas y saludables.</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heart fa-beat"></i>
            <p id="loadingText">Iniciando sesión...</p>
        </div>
    </div>

    <script src="js/supabase-client.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginPage();
        });

        function initializeLoginPage() {
            // Verificar si ya está autenticado
            checkAuthState();
            
            // Event listeners
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('googleLoginBtn').addEventListener('click', handleGoogleLogin);
        }

        async function checkAuthState() {
            const { success, user } = await SupabaseAuth.getCurrentUser();
            if (success && user) {
                // Ya está autenticado, redirigir al dashboard
                window.location.href = 'dashboard.html';
            }
        }

        async function handleLogin(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');

            if (!email || !password) {
                showError('Por favor completa todos los campos');
                return;
            }

            try {
                showLoading(true);
                
                const result = await SupabaseAuth.signIn(email, password);
                
                if (result.success) {
                    showSuccess('¡Sesión iniciada exitosamente!');
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1000);
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('Error:', error);
                showError(error.message || 'Error al iniciar sesión');
            } finally {
                showLoading(false);
            }
        }

        async function handleGoogleLogin() {
            try {
                showLoading(true);
                
                const result = await SupabaseAuth.signInWithGoogle();
                
                if (result.success) {
                    showSuccess('Redirigiendo a Google...');
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('Error:', error);
                showError(error.message || 'Error al iniciar sesión con Google');
                showLoading(false);
            }
        }

        function showLoading(show) {
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loginBtn = document.getElementById('loginBtn');
            const googleBtn = document.getElementById('googleLoginBtn');
            
            if (loadingOverlay) {
                loadingOverlay.style.display = show ? 'flex' : 'none';
            }
            
            if (loginBtn) {
                loginBtn.disabled = show;
                loginBtn.innerHTML = show ? 
                    '<i class="fas fa-spinner fa-spin"></i> Iniciando...' : 
                    '<i class="fas fa-sign-in-alt"></i> Iniciar Sesión';
            }
            
            if (googleBtn) {
                googleBtn.disabled = show;
            }
        }

        function showError(message) {
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            alert('Éxito: ' + message);
        }
    </script>
</body>
</html>
