<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ClaroVínculo - Relaciones Auténticas</title>
    <link rel="stylesheet" href="styles/main.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Supabase SDK -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-heart"></i>
                    <h1><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
                </div>
                <nav class="header-nav">
                    <a href="login.html" class="nav-link">Iniciar Sesión</a>
                    <a href="register.html" class="nav-link btn-outline">Registrarse</a>
                </nav>
            </div>
            <p class="tagline">Facilitando conexiones auténticas y relaciones saludables</p>
        </header>

        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h2>¿Quieres conocer los verdaderos sentimientos de alguien?</h2>
                <p>ClaroVínculo te ayuda a crear un espacio seguro para la comunicación honesta, eliminando el miedo al rechazo y los malentendidos.</p>
                
                <div class="features">
                    <div class="feature">
                        <i class="fas fa-shield-alt"></i>
                        <span>Comunicación segura y privada</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-brain"></i>
                        <span>Análisis inteligente de compatibilidad</span>
                    </div>
                    <div class="feature">
                        <i class="fas fa-heart-pulse"></i>
                        <span>Enfoque en relaciones saludables</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Create Invitation Form -->
        <section class="create-invitation">
            <div class="form-container">
                <h3>Crear Nueva Invitación</h3>
                <p>Invita a alguien a participar en un diálogo honesto y respetuoso</p>
                
                <form id="invitationForm" class="invitation-form">
                    <div class="form-group">
                        <label for="senderName">Tu nombre</label>
                        <input type="text" id="senderName" name="senderName" required 
                               placeholder="¿Cómo te llamas?">
                    </div>

                    <div class="form-group">
                        <label for="recipientName">Nombre de la persona (opcional)</label>
                        <input type="text" id="recipientName" name="recipientName" 
                               placeholder="¿Cómo se llama la persona?">
                    </div>

                    <div class="form-group">
                        <label for="relationshipType">Tipo de relación</label>
                        <select id="relationshipType" name="relationshipType" required>
                            <option value="">Selecciona el tipo de relación</option>
                            <option value="nueva_conexion">Personas que se están conociendo</option>
                            <option value="pareja_establecida">Pareja establecida</option>
                            <option value="amistad">Amistad</option>
                            <option value="familia">Relación familiar</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="customMessage">Mensaje personalizado (opcional)</label>
                        <textarea id="customMessage" name="customMessage" rows="3" 
                                  placeholder="Añade un mensaje personal para explicar por qué es importante para ti..."></textarea>
                    </div>

                    <button type="submit" class="btn-primary" id="createBtn">
                        <i class="fas fa-paper-plane"></i>
                        Crear Invitación
                    </button>
                </form>
            </div>
        </section>

        <!-- Result Section -->
        <section id="resultSection" class="result-section" style="display: none;">
            <div class="result-container">
                <div class="success-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>¡Invitación creada exitosamente!</h3>
                <p>Comparte este enlace con la persona que quieres invitar:</p>
                
                <div class="link-container">
                    <input type="text" id="invitationLink" readonly>
                    <button id="copyLinkBtn" class="btn-copy">
                        <i class="fas fa-copy"></i>
                        Copiar
                    </button>
                </div>
                
                <div class="share-options">
                    <p>O comparte directamente:</p>
                    <div class="share-buttons">
                        <button id="shareWhatsApp" class="btn-share whatsapp">
                            <i class="fab fa-whatsapp"></i>
                            WhatsApp
                        </button>
                        <button id="shareEmail" class="btn-share email">
                            <i class="fas fa-envelope"></i>
                            Email
                        </button>
                        <button id="shareTelegram" class="btn-share telegram">
                            <i class="fab fa-telegram"></i>
                            Telegram
                        </button>
                    </div>
                </div>

                <button id="createAnotherBtn" class="btn-secondary">
                    Crear otra invitación
                </button>
            </div>
        </section>

        <!-- How it works -->
        <section class="how-it-works">
            <h3>¿Cómo funciona?</h3>
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>Crea tu invitación</h4>
                    <p>Completa el formulario con tu información y el tipo de relación que quieres explorar.</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>Comparte el enlace</h4>
                    <p>Envía el enlace generado a la persona que quieres invitar a participar.</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>Respuesta honesta</h4>
                    <p>La persona responde un cuestionario diseñado para revelar sentimientos auténticos.</p>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>Análisis inteligente</h4>
                    <p>Recibe un análisis detallado con insights y recomendaciones para tu relación.</p>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 ClaroVínculo. Facilitando relaciones auténticas y saludables.</p>
            <div class="footer-links">
                <a href="#privacy">Privacidad</a>
                <a href="#terms">Términos</a>
                <a href="#contact">Contacto</a>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-heart fa-beat"></i>
            <p>Creando tu invitación...</p>
        </div>
    </div>

    <script src="js/supabase-client.js"></script>
    <script src="js/app-supabase.js"></script>

    <script>
        // Manejar tokens OAuth si están presentes en la URL
        document.addEventListener('DOMContentLoaded', function() {
            const hash = window.location.hash;

            if (hash && hash.includes('access_token')) {
                console.log('Tokens OAuth detectados en página principal');

                // Mostrar mensaje de éxito
                const successMessage = document.createElement('div');
                successMessage.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #48bb78;
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    z-index: 1000;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;
                successMessage.innerHTML = '✅ ¡Autenticación con Google exitosa!';
                document.body.appendChild(successMessage);

                // Limpiar la URL después de un momento
                setTimeout(() => {
                    window.history.replaceState({}, document.title, window.location.pathname);
                    successMessage.remove();
                }, 3000);

                // Verificar el estado de autenticación
                setTimeout(async () => {
                    const { success, user } = await SupabaseAuth.getCurrentUser();
                    if (success && user) {
                        console.log('Usuario autenticado:', user);
                        // Actualizar la UI si es necesario
                        if (typeof updateAuthUI === 'function') {
                            updateAuthUI(user);
                        }
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>
