# Configuración de GitHub Pages para ClaroVínculo

## 1. Preparar el Repositorio

### Estructura de archivos para GitHub Pages:
```
/
├── index.html (página principal)
├── invitation.html (página de invitaciones)
├── login.html (página de login)
├── register.html (página de registro)
├── dashboard.html (dashboard de usuario)
├── questionnaire.html (cuestionario)
├── results.html (resultados)
├── styles/
│   └── main.css
├── js/
│   ├── supabase-client.js
│   ├── app-supabase.js
│   ├── auth-supabase.js
│   ├── dashboard-supabase.js
│   ├── questionnaire-supabase.js
│   └── results-supabase.js
└── README.md
```

## 2. Configurar GitHub Pages

1. **Crear repositorio en GitHub**:
   - Ve a GitHub y crea un nuevo repositorio
   - Nombre sugerido: `clarovinculo-app`
   - <PERSON>zlo público

2. **Subir archivos**:
   ```bash
   git init
   git add .
   git commit -m "Initial commit - ClaroVínculo app"
   git branch -M main
   git remote add origin https://github.com/TU-USUARIO/clarovinculo-app.git
   git push -u origin main
   ```

3. **Activar GitHub Pages**:
   - Ve a Settings > Pages en tu repositorio
   - Source: Deploy from a branch
   - Branch: main
   - Folder: / (root)
   - Save

4. **Tu app estará disponible en**:
   `https://TU-USUARIO.github.io/clarovinculo-app/`

## 3. Configurar Supabase para GitHub Pages

### En el dashboard de Supabase:

1. **Authentication > URL Configuration**:
   - Site URL: `https://TU-USUARIO.github.io/clarovinculo-app/`
   - Redirect URLs: 
     - `https://TU-USUARIO.github.io/clarovinculo-app/`
     - `https://TU-USUARIO.github.io/clarovinculo-app/dashboard.html`

2. **API Settings > CORS**:
   - Agregar: `https://TU-USUARIO.github.io`

## 4. Actualizar Configuración

### Editar `js/supabase-client.js`:
```javascript
const SUPABASE_CONFIG = {
  url: 'https://tu-proyecto-id.supabase.co',
  anonKey: 'tu-anon-key-aqui'
};
```

## 5. Archivos que necesitas renombrar/actualizar:

- `public/index.html` → `index.html`
- `public/invitation-supabase.html` → `invitation.html`
- `public/login-supabase.html` → `login.html`
- `public/register-supabase.html` → `register.html`
- Todos los archivos de `public/js/` → `js/`
- Todos los archivos de `public/styles/` → `styles/`

## 6. Actualizar rutas en HTML:

Cambiar todas las rutas de:
- `styles/main.css` (ya están correctas)
- `js/archivo.js` (ya están correctas)

## 7. Configurar Google OAuth (opcional):

1. **Google Cloud Console**:
   - Ve a console.cloud.google.com
   - Crea un proyecto o usa uno existente
   - Habilita Google+ API
   - Crea credenciales OAuth 2.0
   - Authorized redirect URIs:
     - `https://tu-proyecto.supabase.co/auth/v1/callback`

2. **En Supabase**:
   - Authentication > Providers > Google
   - Habilitar Google provider
   - Agregar Client ID y Client Secret

## 8. Testing Local:

Para probar localmente antes de subir:
```bash
# Instalar un servidor local simple
npm install -g http-server

# Ejecutar desde la carpeta del proyecto
http-server . -p 8080

# Abrir http://localhost:8080
```

## 9. Dominio Personalizado (opcional):

Si tienes un dominio propio:
1. Crear archivo `CNAME` en la raíz con tu dominio
2. Configurar DNS de tu dominio
3. Actualizar URLs en Supabase

## 10. Variables de Entorno para Producción:

Crear `js/config.js`:
```javascript
const CONFIG = {
  ENVIRONMENT: 'production',
  SUPABASE_URL: 'https://tu-proyecto.supabase.co',
  SUPABASE_ANON_KEY: 'tu-anon-key',
  APP_URL: 'https://tu-usuario.github.io/clarovinculo-app'
};
```

## 11. Ventajas de esta Arquitectura:

✅ **Gratis**: GitHub Pages es gratuito
✅ **Escalable**: Supabase maneja la carga
✅ **Seguro**: Autenticación robusta
✅ **Rápido**: CDN global de GitHub
✅ **Fácil**: Deploy automático con git push
✅ **Profesional**: Dominio personalizado disponible
