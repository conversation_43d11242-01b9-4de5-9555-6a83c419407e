const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const DB_PATH = process.env.DB_PATH || './database/clarovinculo.db';

// Asegurar que el directorio de la base de datos existe
const dbDir = path.dirname(DB_PATH);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

const db = new sqlite3.Database(DB_PATH);

const initDatabase = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Tabla de usuarios (estructura básica primero)
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          email TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Tabla de invitaciones
      db.run(`
        CREATE TABLE IF NOT EXISTS invitations (
          id TEXT PRIMARY KEY,
          sender_id TEXT NOT NULL,
          sender_name TEXT NOT NULL,
          recipient_name TEXT,
          relationship_type TEXT NOT NULL,
          message TEXT,
          status TEXT DEFAULT 'pending',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          responded_at DATETIME,
          FOREIGN KEY (sender_id) REFERENCES users (id)
        )
      `);

      // Tabla de respuestas de cuestionarios
      db.run(`
        CREATE TABLE IF NOT EXISTS questionnaire_responses (
          id TEXT PRIMARY KEY,
          invitation_id TEXT NOT NULL,
          respondent_name TEXT,
          responses TEXT NOT NULL,
          completed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (invitation_id) REFERENCES invitations (id)
        )
      `);

      // Tabla de análisis
      db.run(`
        CREATE TABLE IF NOT EXISTS analysis_results (
          id TEXT PRIMARY KEY,
          response_id TEXT NOT NULL,
          analysis_data TEXT NOT NULL,
          insights TEXT NOT NULL,
          recommendations TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (response_id) REFERENCES questionnaire_responses (id)
        )
      `);

      // Tabla de configuraciones de cuestionarios
      db.run(`
        CREATE TABLE IF NOT EXISTS questionnaire_configs (
          id TEXT PRIMARY KEY,
          relationship_type TEXT NOT NULL,
          questions TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `, async (err) => {
        if (err) {
          reject(err);
        } else {
          try {
            // Ejecutar migración para agregar columnas faltantes
            const migrate = require('./migrate');
            await migrate();
            console.log('✅ Base de datos inicializada correctamente');
            resolve(db);
          } catch (migrateErr) {
            console.error('❌ Error en migración:', migrateErr);
            reject(migrateErr);
          }
        }
      });
    });
  });
};

module.exports = initDatabase;
module.exports.db = db;
