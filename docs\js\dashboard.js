// Dashboard functionality
let currentUser = null;
let currentSection = 'dashboard';

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

async function initializeDashboard() {
    try {
        // Load user data
        await loadUserData();
        
        // Setup navigation
        setupNavigation();
        
        // Setup event listeners
        setupEventListeners();
        
        // Load initial section
        showSection('dashboard');
        
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        ClaroVinculo.showError('Error al cargar el dashboard');
        
        // Redirect to login if unauthorized
        if (error.message.includes('401')) {
            window.location.href = '/login?error=session_expired';
        }
    }
}

async function loadUserData() {
    try {
        const response = await fetch('/api/auth/dashboard', {
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            currentUser = result.user;
            updateUserInfo(result.user);
            updateStats(result.stats);
        } else {
            throw new Error(result.error || 'Error al cargar datos del usuario');
        }
        
    } catch (error) {
        console.error('Error loading user data:', error);
        throw error;
    }
}

function updateUserInfo(user) {
    const userInfo = document.getElementById('userInfo');
    const initials = user.name.split(' ').map(n => n[0]).join('').toUpperCase();
    
    userInfo.innerHTML = `
        <div class="user-avatar">
            ${user.avatar_url ? 
                `<img src="${user.avatar_url}" alt="${user.name}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` :
                initials
            }
        </div>
        <div class="user-details">
            <h4>${user.name}</h4>
            <p>${user.email}</p>
        </div>
    `;
}

function updateStats(stats) {
    document.getElementById('totalInvitations').textContent = stats.total_invitations || 0;
    document.getElementById('acceptedInvitations').textContent = stats.accepted_invitations || 0;
    document.getElementById('pendingInvitations').textContent = stats.pending_invitations || 0;
    document.getElementById('totalResponses').textContent = stats.total_responses || 0;
}

function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item a');
    
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });
}

function setupEventListeners() {
    // Logout button
    document.getElementById('logoutBtn').addEventListener('click', handleLogout);
    
    // Sidebar toggle for mobile
    document.getElementById('sidebarToggle').addEventListener('click', toggleSidebar);
    
    // New invitation buttons
    document.getElementById('newInvitationBtn').addEventListener('click', () => {
        window.location.href = '/';
    });
    
    const createInvitationBtn = document.getElementById('createInvitationBtn');
    if (createInvitationBtn) {
        createInvitationBtn.addEventListener('click', () => {
            window.location.href = '/';
        });
    }
    
    // Quick action buttons
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.getAttribute('data-action');
            handleQuickAction(action);
        });
    });
    
    // Profile form
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileUpdate);
    }
    
    // Password form
    const passwordForm = document.getElementById('passwordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', handlePasswordChange);
    }
}

function showSection(sectionName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    document.querySelector(`[data-section="${sectionName}"]`).parentElement.classList.add('active');
    
    // Update content
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    document.getElementById(`${sectionName}-section`).classList.add('active');
    
    // Update page title
    const titles = {
        dashboard: 'Dashboard',
        invitations: 'Mis Invitaciones',
        responses: 'Respuestas',
        analytics: 'Análisis',
        profile: 'Mi Perfil'
    };
    
    document.getElementById('pageTitle').textContent = titles[sectionName] || 'Dashboard';
    
    // Load section-specific data
    loadSectionData(sectionName);
    
    currentSection = sectionName;
}

async function loadSectionData(section) {
    switch (section) {
        case 'dashboard':
            await loadRecentActivity();
            break;
        case 'invitations':
            await loadInvitations();
            break;
        case 'responses':
            await loadResponses();
            break;
        case 'analytics':
            await loadAnalytics();
            break;
        case 'profile':
            loadProfileData();
            break;
    }
}

async function loadRecentActivity() {
    try {
        // This would typically fetch from an API
        const activityList = document.getElementById('recentActivity');
        
        // Mock data for now
        activityList.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="activity-content">
                    <p><strong>Nueva invitación enviada</strong></p>
                    <span class="activity-time">Hace 2 horas</span>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="activity-content">
                    <p><strong>Invitación aceptada</strong></p>
                    <span class="activity-time">Hace 1 día</span>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="activity-content">
                    <p><strong>Nueva respuesta recibida</strong></p>
                    <span class="activity-time">Hace 2 días</span>
                </div>
            </div>
        `;
        
    } catch (error) {
        console.error('Error loading recent activity:', error);
    }
}

async function loadInvitations() {
    try {
        // This would fetch invitations from API
        const invitationsList = document.getElementById('invitationsList');
        
        // Mock data for now
        invitationsList.innerHTML = `
            <div class="invitation-card">
                <div class="invitation-header">
                    <h4>Invitación a María</h4>
                    <span class="status-badge pending">Pendiente</span>
                </div>
                <p>Tipo: Nueva Conexión</p>
                <p>Enviada: Hace 2 horas</p>
                <div class="invitation-actions">
                    <button class="btn-secondary">Ver Detalles</button>
                    <button class="btn-primary">Reenviar</button>
                </div>
            </div>
        `;
        
    } catch (error) {
        console.error('Error loading invitations:', error);
    }
}

async function loadResponses() {
    try {
        // This would fetch responses from API
        const responsesList = document.getElementById('responsesList');
        
        // Mock data for now
        responsesList.innerHTML = `
            <div class="response-card">
                <div class="response-header">
                    <h4>Respuesta de Carlos</h4>
                    <span class="response-date">Hace 1 día</span>
                </div>
                <p>Tipo de relación: Amistad</p>
                <div class="response-actions">
                    <button class="btn-primary">Ver Análisis</button>
                </div>
            </div>
        `;
        
    } catch (error) {
        console.error('Error loading responses:', error);
    }
}

async function loadAnalytics() {
    try {
        // This would generate charts and analytics
        console.log('Loading analytics...');
        
    } catch (error) {
        console.error('Error loading analytics:', error);
    }
}

function loadProfileData() {
    if (currentUser) {
        document.getElementById('profileName').value = currentUser.name || '';
        document.getElementById('profileEmail').value = currentUser.email || '';
    }
}

function handleQuickAction(action) {
    switch (action) {
        case 'new-invitation':
            window.location.href = '/';
            break;
        case 'view-analytics':
            showSection('analytics');
            break;
        case 'export-data':
            ClaroVinculo.showNotification('Función de exportación en desarrollo', 'info');
            break;
    }
}

async function handleLogout() {
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Cerrando sesión...';
        
        const response = await fetch('/api/auth/logout', {
            method: 'POST',
            credentials: 'include'
        });
        
        const result = await response.json();
        
        if (result.success) {
            ClaroVinculo.showSuccess('Sesión cerrada exitosamente');
            
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        } else {
            throw new Error(result.error || 'Error al cerrar sesión');
        }
        
    } catch (error) {
        console.error('Error en logout:', error);
        ClaroVinculo.showError(error.message || 'Error al cerrar sesión');
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

async function handleProfileUpdate(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name'),
        email: formData.get('email')
    };
    
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Actualizando perfil...';
        
        const response = await fetch('/api/auth/profile', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentUser = result.user;
            updateUserInfo(result.user);
            ClaroVinculo.showSuccess('Perfil actualizado exitosamente');
        } else {
            throw new Error(result.error || 'Error al actualizar perfil');
        }
        
    } catch (error) {
        console.error('Error updating profile:', error);
        ClaroVinculo.showError(error.message || 'Error al actualizar perfil');
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

async function handlePasswordChange(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        currentPassword: formData.get('currentPassword'),
        newPassword: formData.get('newPassword'),
        confirmNewPassword: formData.get('confirmNewPassword')
    };
    
    // Validation
    if (data.newPassword !== data.confirmNewPassword) {
        ClaroVinculo.showError('Las nuevas contraseñas no coinciden');
        return;
    }
    
    if (data.newPassword.length < 6) {
        ClaroVinculo.showError('La nueva contraseña debe tener al menos 6 caracteres');
        return;
    }
    
    try {
        ClaroVinculo.showLoading(true);
        document.getElementById('loadingText').textContent = 'Cambiando contraseña...';
        
        const response = await fetch('/api/auth/password', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                currentPassword: data.currentPassword,
                newPassword: data.newPassword
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            ClaroVinculo.showSuccess('Contraseña actualizada exitosamente');
            e.target.reset();
        } else {
            throw new Error(result.error || 'Error al cambiar contraseña');
        }
        
    } catch (error) {
        console.error('Error changing password:', error);
        ClaroVinculo.showError(error.message || 'Error al cambiar contraseña');
    } finally {
        ClaroVinculo.showLoading(false);
    }
}

function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('mobile-hidden');
}

// Export functions for use in other scripts
window.DashboardModule = {
    showSection,
    loadUserData,
    handleLogout
};
