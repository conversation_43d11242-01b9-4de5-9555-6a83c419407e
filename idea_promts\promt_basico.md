Asume el rol de una inteligencia experta con amplios conocimientos en psicología, sociología y relaciones humanas; con dominio profundo en dinámicas de pareja, desarrollo personal, ingeniería de software y estrategias avanzadas de marketing digital. A partir de esta perspectiva multidisciplinar, crea un prompt detallado y optimizado para instruir a una inteligencia artificial en la creación de la siguiente aplicación:   una app web que sirve para poder establecer relaciones sanas, entre parejas, familiares ya migos, incluso personas que recien se estan conociendo y que quieren saber si la otra está interesada y en que proporciones o en qué ambitos, primero que nada quiero estructurar la idea, se me ocurren cosas como: alguien crea su perfil y añade una posible relacion (o no sé como llamarlo) y estipula qué cosas quuiere sabe ro algo por defecto, el caso es que me imagino que le envía a esa persona un link y la otra lo abre y está una foto del remitente y un mensaje como, hola x persona te ha querido invitar a dialogar (o usar la frase más acorde) para aclarar (o entenderse, o conocerse, o lo que aplique mejor), te gustaría aceptarla? sólo serían unos minutos, y significa mucho para x persona. Entonces ya ahí mismo se evalúa todo: el tiempo en que tarda en responder, si decide responder o no, si decide enviar una respuesta de por qué no decide responder, etc, luego si acepta se le invita a crear su perfil pero algo muy rapido con google o así (o igual no sea necesario, sino más adelante) el caso es que se le harían preguntas un test ( o no sé si primero un video, texto o voz d ela persona remitente dando un pequeño saludo o explicación del asunto) y acto seguiido se inicia la encuesta: depende del tipo de relación que haya elegido la persona o el tipo de encuesta, suponiendo q en este caso se estan conociendo y el remitente quiere saber el interes y/o la disponibilidad emocional, entimental, legal, etc que tiene la otra y su vision de futuro ect, entonces hace la encuesta, pero la idea es que las encuestas sean algo directas, mmuy directas, que vayan al grano (o como consideres ma sadecuaso) en plan: te gusta x persona? de una a 10 cuanto crees que podría ser la atraccion fisica que soentes?, y su actitud? te gustaría seguirla conociendo? cual tu estado emocional al verla? ahora mismo estas en una relacion? estas disponible sentimentalmente? te ves en una relacion de noviasgo con x persona? o elige en que tipo de relacion te ves ahora mismo con x persona? y así sucesivamente, y las preguntas tendrían que estar encadenadas en base al tip de respuestas. el caso es que para cuando la personas termine la encusta, se presente un resumen mediante un análisi de ia, y s ele presente bien organizado a la persona, y se le dice: en base a lo que has elegido hemos analisado lo siguiente: (por ejemplo) nos hemos dado cuenta de que en estos momentos no te encuentras disponible para enfrentar una relación sntimental seria, quizas porque como has comentado acabas de salir d euna relación y aún n has sanado ni has terminado de cerra ese ciclo, es impòrtante saber eso, así te evitas pasar tu mismo malos momentos y tambien a la otra persona. te gustaría enviarle la siguiente respuesta a x persona? y así, entonces lo mismo para todos, parejas que llevan muchos años, familiares, amigos, etc, obviamente cada cosa adpatada a cada tipo de relacion, en alguinos casos para saber las probabilidades de tener algo, otras para saber como se sienten, medir el estao de salud de la relacion, en otras para saber si puedne mejorar algo, otras para saber si se pueden perdonar y sgeuir adelante, otras para despedirse, etc, etc, etc, entonces había pensado en un nombre ocmo: healthy heart, o amor y paz, o corazon sano, y así (te djeo a ti como experto)